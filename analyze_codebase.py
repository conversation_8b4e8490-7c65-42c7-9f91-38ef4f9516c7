#!/usr/bin/env python3
"""
Codebase Analysis Script
Comprehensive analysis of the src/ folder structure to understand:
- File organization and dependencies
- Duplicate functionality
- Import patterns
- Code complexity metrics
- Architecture patterns
"""

import os
import ast
import json
import re
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Set, Tuple, Any
from dataclasses import dataclass, asdict
import sys

@dataclass
class FileInfo:
    """Information about a single file"""
    path: str
    size: int
    lines: int
    imports: List[str]
    classes: List[str]
    functions: List[str]
    complexity: int
    dependencies: Set[str]
    dependents: Set[str]

@dataclass
class ModuleInfo:
    """Information about a module/directory"""
    path: str
    file_count: int
    total_lines: int
    total_size: int
    files: List[FileInfo]
    submodules: List[str]
    purpose: str = ""

@dataclass
class CodebaseAnalysis:
    """Complete codebase analysis"""
    total_files: int
    total_lines: int
    total_size: int
    modules: Dict[str, ModuleInfo]
    duplicates: List[Tuple[str, str]]
    circular_deps: List[List[str]]
    complexity_hotspots: List[Tuple[str, int]]
    import_patterns: Dict[str, int]
    architecture_issues: List[str]

class CodebaseAnalyzer:
    """Analyzes the codebase structure and dependencies"""
    
    def __init__(self, src_path: str):
        self.src_path = Path(src_path)
        self.files: Dict[str, FileInfo] = {}
        self.modules: Dict[str, ModuleInfo] = {}
        self.import_graph: Dict[str, Set[str]] = defaultdict(set)
        self.reverse_import_graph: Dict[str, Set[str]] = defaultdict(set)
        
    def analyze(self) -> CodebaseAnalysis:
        """Perform complete codebase analysis"""
        print("🔍 Starting codebase analysis...")
        
        # Step 1: Scan all files
        self._scan_files()
        
        # Step 2: Analyze imports and dependencies
        self._analyze_imports()
        
        # Step 3: Organize into modules
        self._organize_modules()
        
        # Step 4: Find duplicates
        duplicates = self._find_duplicates()
        
        # Step 5: Find circular dependencies
        circular_deps = self._find_circular_dependencies()
        
        # Step 6: Find complexity hotspots
        complexity_hotspots = self._find_complexity_hotspots()
        
        # Step 7: Analyze import patterns
        import_patterns = self._analyze_import_patterns()
        
        # Step 8: Identify architecture issues
        architecture_issues = self._identify_architecture_issues()
        
        return CodebaseAnalysis(
            total_files=len(self.files),
            total_lines=sum(f.lines for f in self.files.values()),
            total_size=sum(f.size for f in self.files.values()),
            modules=self.modules,
            duplicates=duplicates,
            circular_deps=circular_deps,
            complexity_hotspots=complexity_hotspots,
            import_patterns=import_patterns,
            architecture_issues=architecture_issues
        )
    
    def _scan_files(self):
        """Scan all Python files in the codebase"""
        print("📁 Scanning files...")
        
        for py_file in self.src_path.rglob("*.py"):
            if "__pycache__" in str(py_file):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Parse AST
                tree = ast.parse(content)
                
                # Extract information
                imports = self._extract_imports(tree)
                classes = self._extract_classes(tree)
                functions = self._extract_functions(tree)
                complexity = self._calculate_complexity(tree)
                
                # Create file info
                rel_path = str(py_file.relative_to(self.src_path))
                file_info = FileInfo(
                    path=rel_path,
                    size=py_file.stat().st_size,
                    lines=len(content.splitlines()),
                    imports=imports,
                    classes=classes,
                    functions=functions,
                    complexity=complexity,
                    dependencies=set(),
                    dependents=set()
                )
                
                self.files[rel_path] = file_info
                
            except Exception as e:
                print(f"⚠️  Error parsing {py_file}: {e}")
    
    def _extract_imports(self, tree) -> List[str]:
        """Extract all imports from AST"""
        imports = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ""
                for alias in node.names:
                    imports.append(f"{module}.{alias.name}" if module else alias.name)
        return imports
    
    def _extract_classes(self, tree) -> List[str]:
        """Extract class names from AST"""
        classes = []
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                classes.append(node.name)
        return classes
    
    def _extract_functions(self, tree) -> List[str]:
        """Extract function names from AST"""
        functions = []
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                functions.append(node.name)
        return functions
    
    def _calculate_complexity(self, tree) -> int:
        """Calculate cyclomatic complexity"""
        complexity = 1  # Base complexity
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor, ast.ExceptHandler)):
                complexity += 1
            elif isinstance(node, ast.BoolOp):
                complexity += len(node.values) - 1
        return complexity
    
    def _analyze_imports(self):
        """Analyze import relationships"""
        print("🔗 Analyzing imports...")
        
        for file_path, file_info in self.files.items():
            for import_name in file_info.imports:
                # Try to resolve import to actual file
                resolved_path = self._resolve_import(import_name, file_path)
                if resolved_path:
                    self.import_graph[file_path].add(resolved_path)
                    self.reverse_import_graph[resolved_path].add(file_path)
                    file_info.dependencies.add(resolved_path)
                    if resolved_path in self.files:
                        self.files[resolved_path].dependents.add(file_path)
    
    def _resolve_import(self, import_name: str, from_file: str) -> str:
        """Try to resolve import to actual file path"""
        # Handle relative imports
        if import_name.startswith('.'):
            from_dir = Path(from_file).parent
            parts = import_name.split('.')
            # Skip the first dot
            for part in parts[1:]:
                if part:
                    from_dir = from_dir / part
            return str(from_dir.relative_to(self.src_path))
        
        # Handle absolute imports starting with src
        if import_name.startswith('src.'):
            return import_name[4:]  # Remove 'src.' prefix
        
        # Try to find the file
        for py_file in self.src_path.rglob("*.py"):
            if "__pycache__" in str(py_file):
                continue
            rel_path = str(py_file.relative_to(self.src_path))
            if rel_path.replace('/', '.').replace('.py', '') == import_name:
                return rel_path
        
        return None
    
    def _organize_modules(self):
        """Organize files into modules"""
        print("📦 Organizing modules...")
        
        for file_path, file_info in self.files.items():
            # Get module path (directory)
            module_path = str(Path(file_path).parent)
            if module_path == ".":
                module_path = "root"
            
            if module_path not in self.modules:
                self.modules[module_path] = ModuleInfo(
                    path=module_path,
                    file_count=0,
                    total_lines=0,
                    total_size=0,
                    files=[],
                    submodules=[]
                )
            
            self.modules[module_path].files.append(file_info)
            self.modules[module_path].file_count += 1
            self.modules[module_path].total_lines += file_info.lines
            self.modules[module_path].total_size += file_info.size
    
    def _find_duplicates(self) -> List[Tuple[str, str]]:
        """Find duplicate files based on content similarity"""
        print("🔍 Finding duplicates...")
        
        duplicates = []
        file_contents = {}
        
        # Group files by size first (quick filter)
        size_groups = defaultdict(list)
        for file_path, file_info in self.files.items():
            size_groups[file_info.size].append(file_path)
        
        # Check files with same size
        for size, files in size_groups.items():
            if len(files) > 1:
                for i, file1 in enumerate(files):
                    for file2 in files[i+1:]:
                        if self._files_similar(file1, file2):
                            duplicates.append((file1, file2))
        
        return duplicates
    
    def _files_similar(self, file1: str, file2: str) -> bool:
        """Check if two files are similar"""
        try:
            with open(self.src_path / file1, 'r') as f1, open(self.src_path / file2, 'r') as f2:
                content1 = f1.read()
                content2 = f2.read()
                
                # Simple similarity check (could be improved)
                if content1 == content2:
                    return True
                
                # Check if one is subset of another
                if content1 in content2 or content2 in content1:
                    return True
                
                # Check class/function similarity
                classes1 = set(self.files[file1].classes)
                classes2 = set(self.files[file2].classes)
                if classes1 and classes2 and classes1 == classes2:
                    return True
                
        except Exception:
            pass
        
        return False
    
    def _find_circular_dependencies(self) -> List[List[str]]:
        """Find circular dependencies using DFS"""
        print("🔄 Finding circular dependencies...")
        
        visited = set()
        rec_stack = set()
        cycles = []
        
        def dfs(node, path):
            if node in rec_stack:
                # Found a cycle
                cycle_start = path.index(node)
                cycle = path[cycle_start:] + [node]
                cycles.append(cycle)
                return
            
            if node in visited:
                return
            
            visited.add(node)
            rec_stack.add(node)
            path.append(node)
            
            for neighbor in self.import_graph.get(node, []):
                dfs(neighbor, path.copy())
            
            rec_stack.remove(node)
        
        for node in self.files:
            if node not in visited:
                dfs(node, [])
        
        return cycles
    
    def _find_complexity_hotspots(self) -> List[Tuple[str, int]]:
        """Find files with high complexity"""
        print("🔥 Finding complexity hotspots...")
        
        hotspots = [(path, info.complexity) for path, info in self.files.items()]
        hotspots.sort(key=lambda x: x[1], reverse=True)
        return hotspots[:20]  # Top 20 most complex files
    
    def _analyze_import_patterns(self) -> Dict[str, int]:
        """Analyze import patterns"""
        print("📊 Analyzing import patterns...")
        
        patterns = Counter()
        for file_info in self.files.values():
            for import_name in file_info.imports:
                if import_name.startswith('src.'):
                    patterns['src_absolute'] += 1
                elif import_name.startswith('.'):
                    patterns['relative'] += 1
                elif '.' in import_name:
                    patterns['absolute'] += 1
                else:
                    patterns['simple'] += 1
        
        return dict(patterns)
    
    def _identify_architecture_issues(self) -> List[str]:
        """Identify architectural issues"""
        print("🏗️  Identifying architecture issues...")
        
        issues = []
        
        # Check for deep nesting
        for module_path in self.modules:
            depth = len(module_path.split('/'))
            if depth > 4:
                issues.append(f"Deep nesting in {module_path} ({depth} levels)")
        
        # Check for large modules
        for module_path, module in self.modules.items():
            if module.file_count > 20:
                issues.append(f"Large module {module_path} ({module.file_count} files)")
            if module.total_lines > 5000:
                issues.append(f"Large module {module_path} ({module.total_lines} lines)")
        
        # Check for circular dependencies
        if self._find_circular_dependencies():
            issues.append(f"Circular dependencies found ({len(self._find_circular_dependencies())} cycles)")
        
        # Check for duplicate functionality
        duplicate_names = defaultdict(list)
        for file_path, file_info in self.files.items():
            filename = Path(file_path).name
            duplicate_names[filename].append(file_path)
        
        for filename, paths in duplicate_names.items():
            if len(paths) > 1:
                issues.append(f"Duplicate filename {filename} in {len(paths)} locations")
        
        return issues

def generate_report(analysis: CodebaseAnalysis, output_file: str = "codebase_analysis_report.json"):
    """Generate detailed analysis report"""
    print(f"📝 Generating report: {output_file}")
    
    # Convert to serializable format
    report = {
        "summary": {
            "total_files": analysis.total_files,
            "total_lines": analysis.total_lines,
            "total_size_mb": round(analysis.total_size / (1024 * 1024), 2),
            "modules_count": len(analysis.modules),
            "duplicates_count": len(analysis.duplicates),
            "circular_deps_count": len(analysis.circular_deps),
            "architecture_issues_count": len(analysis.architecture_issues)
        },
        "modules": {path: asdict(module) for path, module in analysis.modules.items()},
        "duplicates": analysis.duplicates,
        "circular_dependencies": analysis.circular_deps,
        "complexity_hotspots": analysis.complexity_hotspots,
        "import_patterns": analysis.import_patterns,
        "architecture_issues": analysis.architecture_issues
    }
    
    with open(output_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"✅ Report saved to {output_file}")

def print_summary(analysis: CodebaseAnalysis):
    """Print analysis summary"""
    print("\n" + "="*80)
    print("📊 CODEBASE ANALYSIS SUMMARY")
    print("="*80)
    
    print(f"📁 Total Files: {analysis.total_files}")
    print(f"📄 Total Lines: {analysis.total_lines:,}")
    print(f"💾 Total Size: {analysis.total_size / (1024*1024):.1f} MB")
    print(f"📦 Modules: {len(analysis.modules)}")
    
    print(f"\n🔍 DUPLICATES: {len(analysis.duplicates)}")
    for dup in analysis.duplicates[:5]:  # Show first 5
        print(f"  - {dup[0]} <-> {dup[1]}")
    
    print(f"\n🔄 CIRCULAR DEPENDENCIES: {len(analysis.circular_deps)}")
    for cycle in analysis.circular_deps[:3]:  # Show first 3
        print(f"  - {' -> '.join(cycle)}")
    
    print(f"\n🔥 COMPLEXITY HOTSPOTS (Top 5):")
    for path, complexity in analysis.complexity_hotspots[:5]:
        print(f"  - {path}: {complexity}")
    
    print(f"\n📊 IMPORT PATTERNS:")
    for pattern, count in analysis.import_patterns.items():
        print(f"  - {pattern}: {count}")
    
    print(f"\n🏗️  ARCHITECTURE ISSUES: {len(analysis.architecture_issues)}")
    for issue in analysis.architecture_issues[:10]:  # Show first 10
        print(f"  - {issue}")
    
    print("\n" + "="*80)

def main():
    """Main analysis function"""
    if len(sys.argv) != 2:
        print("Usage: python analyze_codebase.py <src_path>")
        print("Example: python analyze_codebase.py src(old)")
        sys.exit(1)
    
    src_path = sys.argv[1]
    if not os.path.exists(src_path):
        print(f"Error: Path {src_path} does not exist")
        sys.exit(1)
    
    print("🚀 Starting Codebase Analysis")
    print(f"📂 Analyzing: {src_path}")
    
    analyzer = CodebaseAnalyzer(src_path)
    analysis = analyzer.analyze()
    
    print_summary(analysis)
    generate_report(analysis)
    
    print("\n✅ Analysis complete!")
    print("📄 Detailed report saved to: codebase_analysis_report.json")

if __name__ == "__main__":
    main()
