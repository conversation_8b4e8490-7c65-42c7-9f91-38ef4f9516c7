import asyncio
import logging
from datetime import datetime, time
from typing import Dict, Optional

import pytz
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger

from src.api.data.constants import TOP_SYMBOLS, WEEKEND_DATA_TTL, WEEKEND_BATCH_SIZE
from src.api.data.cache import MarketDataCache, warm_top_symbols_cache
from src.api.data.metrics import cache_metrics
from src.core.config_manager import get_config

logger = logging.getLogger(__name__)

class CacheWarmingScheduler:
    """
    Scheduler for pre-market cache warming jobs.
    """
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler(timezone=pytz.UTC)
        self.cache = MarketDataCache()
        self._job_running = False
        
    async def start(self):
        """Start the scheduler with pre-market cache warming job."""
        # Schedule cache warming 1 hour before market open (8:30 AM ET = 13:30 UTC)
        # Run Monday-Friday at 12:30 UTC (7:30 AM ET)
        self.scheduler.add_job(
            func=self._execute_cache_warming,
            trigger=CronTrigger(
                hour=12,
                minute=30,
                day_of_week='mon-fri',
                timezone=pytz.UTC
            ),
            id='pre_market_cache_warming',
            name='Pre-market Cache Warming',
            replace_existing=True,
            max_instances=1  # Ensure only one instance runs at a time
        )
        
        # Also add a job for weekend/extended hours if needed
        self.scheduler.add_job(
            func=self._execute_weekend_cache_warming,
            trigger=CronTrigger(
                hour=15,  # 3 PM UTC on Sunday (10 AM ET)
                minute=0,
                day_of_week='sun',
                timezone=pytz.UTC
            ),
            id='weekend_cache_warming',
            name='Weekend Cache Warming',
            replace_existing=True,
            max_instances=1
        )
        
        self.scheduler.start()
        logger.info("Cache warming scheduler started")
        
    async def stop(self):
        """Stop the scheduler gracefully."""
        if self.scheduler.running:
            self.scheduler.shutdown(wait=True)
            logger.info("Cache warming scheduler stopped")
    
    async def _execute_cache_warming(self):
        """Execute the main cache warming job."""
        if self._job_running:
            logger.warning("Cache warming job already running, skipping...")
            return
            
        self._job_running = True
        job_start = datetime.now()
        
        try:
            logger.info("Starting pre-market cache warming job")
            
            # Warm cache for top symbols
            results = await warm_top_symbols_cache(self.cache)
            
            # Log results and metrics
            await self._log_job_results(results, job_start, "pre_market")
            
            # Send metrics to monitoring system
            await self._send_metrics(results, job_start)
            
            # Record Prometheus metrics
            await self._record_prometheus_metrics(results, job_start, "pre_market")
            
        except Exception as e:
            logger.error(f"Cache warming job failed: {e}", exc_info=True)
            # Send alert/notification about job failure
            await self._send_failure_alert(e)
            
            # Record failure metrics
            duration = (datetime.now() - job_start).total_seconds()
            cache_metrics.record_cache_warming_job("pre_market", duration, 0, 0)
            
        finally:
            self._job_running = False
            
    async def _execute_weekend_cache_warming(self):
        """Execute weekend cache refresh job."""
        if self._job_running:
            logger.warning("Cache warming job already running, skipping weekend job...")
            return
            
        self._job_running = True
        job_start = datetime.now()
        
        try:
            logger.info("Starting weekend cache warming job")
            
            # Weekend job - refresh with extended data
            results = await self.cache.warm_cache_for_symbols(
                symbols=TOP_SYMBOLS[:20],  # Top 20 only on weekends
                days=60,  # More historical data
                ttl=WEEKEND_DATA_TTL,  # 3 days TTL for weekend
                batch_size=WEEKEND_BATCH_SIZE  # Smaller batches on weekends
            )
            
            await self._log_job_results(results, job_start, "weekend")
            await self._send_metrics(results, job_start)
            
            # Record Prometheus metrics
            await self._record_prometheus_metrics(results, job_start, "weekend")
            
        except Exception as e:
            logger.error(f"Weekend cache warming job failed: {e}", exc_info=True)
            await self._send_failure_alert(e)
            
            # Record failure metrics
            duration = (datetime.now() - job_start).total_seconds()
            cache_metrics.record_cache_warming_job("weekend", duration, 0, 0)
            
        finally:
            self._job_running = False
    
    async def _record_prometheus_metrics(
        self, 
        results: Dict[str, bool], 
        job_start: datetime,
        job_type: str
    ):
        """Record metrics in Prometheus format."""
        try:
            duration = (datetime.now() - job_start).total_seconds()
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            
            # Record cache warming metrics
            cache_metrics.record_cache_warming_job(
                job_type, duration, total_count, success_count
            )
            
            # Record cache statistics
            cache_stats = await self.cache.get_cache_stats()
            if cache_stats:
                cache_metrics.record_cache_stats(cache_stats)
                
            logger.debug(f"Recorded Prometheus metrics for {job_type} job")
            
        except Exception as e:
            logger.error(f"Failed to record Prometheus metrics: {e}")
    
    async def _log_job_results(
        self, 
        results: Dict[str, bool], 
        job_start: datetime,
        job_type: str
    ):
        """Log detailed job execution results."""
        duration = (datetime.now() - job_start).total_seconds()
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        success_rate = (success_count / total_count * 100) if total_count > 0 else 0
        
        logger.info(
            f"{job_type.title()} cache warming completed:\n"
            f"  Duration: {duration:.2f}s\n"
            f"  Success: {success_count}/{total_count} ({success_rate:.1f}%)\n"
            f"  Failed symbols: {[sym for sym, success in results.items() if not success]}"
        )
        
        # Log cache statistics
        cache_stats = await self.cache.get_cache_stats()
        if cache_stats:
            logger.info(
                f"Cache statistics after {job_type} warming:\n"
                f"  Hit rate: {cache_stats.get('hit_rate', 0):.1f}%\n"
                f"  Memory usage: {cache_stats.get('used_memory_human', 'N/A')}\n"
                f"  Total hits: {cache_stats.get('keyspace_hits', 0)}\n"
                f"  Total misses: {cache_stats.get('keyspace_misses', 0)}"
            )
    
    async def _send_metrics(self, results: Dict[str, bool], job_start: datetime):
        """Send metrics to monitoring system (Prometheus/Grafana)."""
        try:
            # This would integrate with your metrics system
            # Example implementation:
            
            duration = (datetime.now() - job_start).total_seconds()
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            
            metrics = {
                'cache_warming_duration_seconds': duration,
                'cache_warming_symbols_total': total_count,
                'cache_warming_symbols_success': success_count,
                'cache_warming_success_rate': success_count / total_count if total_count > 0 else 0,
            }
            
            # Send to your metrics collector
            # await send_to_prometheus(metrics)
            
            logger.debug(f"Sent cache warming metrics: {metrics}")
            
        except Exception as e:
            logger.error(f"Failed to send metrics: {e}")
    
    async def _send_failure_alert(self, error: Exception):
        """Send alert when job fails."""
        try:
            # This would integrate with your alerting system
            alert_message = f"Cache warming job failed: {str(error)}"
            
            # Send to your alerting system
            # await send_slack_alert(alert_message)
            # await send_pagerduty_alert(alert_message)
            
            logger.error(f"Sent failure alert: {alert_message}")
            
        except Exception as e:
            logger.error(f"Failed to send failure alert: {e}")
    
    async def manual_warm_cache(self, symbols: Optional[list] = None) -> Dict[str, bool]:
        """Manually trigger cache warming (useful for testing/debugging)."""
        if self._job_running:
            raise RuntimeError("Cache warming job already running")
            
        symbols = symbols or TOP_SYMBOLS
        logger.info(f"Manual cache warming triggered for {len(symbols)} symbols")
        
        return await warm_top_symbols_cache(self.cache)
    
    def get_job_status(self) -> Dict:
        """Get current job status and next run times."""
        jobs = []
        
        for job in self.scheduler.get_jobs():
            jobs.append({
                'id': job.id,
                'name': job.name,
                'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                'trigger': str(job.trigger)
            })
        
        return {
            'scheduler_running': self.scheduler.running,
            'job_currently_running': self._job_running,
            'jobs': jobs
        }


# Global scheduler instance
cache_warming_scheduler: Optional[CacheWarmingScheduler] = None

async def start_cache_warming_scheduler():
    """Initialize and start the cache warming scheduler."""
    global cache_warming_scheduler
    
    settings = get_config()
    if not getattr(settings, 'ENABLE_CACHE_WARMING', True):
        logger.info("Cache warming disabled in settings")
        return
        
    cache_warming_scheduler = CacheWarmingScheduler()
    await cache_warming_scheduler.start()
    
    return cache_warming_scheduler

async def stop_cache_warming_scheduler():
    """Stop the cache warming scheduler."""
    global cache_warming_scheduler
    
    if cache_warming_scheduler:
        await cache_warming_scheduler.stop()
        cache_warming_scheduler = None

def get_cache_warming_scheduler() -> Optional[CacheWarmingScheduler]:
    """Get the global cache warming scheduler instance."""
    return cache_warming_scheduler


# TOP_SYMBOLS is now imported at the top of the file