import json
import asyncio
from typing import Optional, List, Dict
from datetime import datetime

from redis import asyncio as aioredis
from pydantic import BaseModel

from src.core.config_manager import get_config
from src.shared.error_handling.logging import get_logger
from src.api.data.providers.base import MarketDataResponse
from src.api.data.constants import TOP_SYMBOLS, DEFAULT_CACHE_TTL

logger = get_logger(__name__)

class MarketDataCache:
    """
    Redis-based caching service for market data.
    
    Supports caching of current prices, historical data, and analysis results.
    """
    
    def __init__(
        self, 
        redis_url: Optional[str] = None,
        cache_ttl: int = DEFAULT_CACHE_TTL,
        max_cache_size: int = 1000
    ):
        """
        Initialize market data cache.
        
        Args:
            redis_url (str, optional): Redis connection URL
            cache_ttl (int, optional): Default cache time-to-live in seconds
            max_cache_size (int, optional): Maximum number of cache entries
        """
        self.redis_url = redis_url or get_config().get('redis', 'url')
        self.cache_ttl = cache_ttl
        self.max_cache_size = max_cache_size
        self._redis_client = None
    
    async def _get_redis_client(self):
        """
        Get or create Redis client.
        
        Returns:
            aioredis.Redis: Async Redis client
        """
        if not self._redis_client:
            try:
                self._redis_client = await aioredis.from_url(
                    self.redis_url, 
                    encoding="utf-8", 
                    decode_responses=True
                )
            except (aioredis.ConnectionError, aioredis.TimeoutError) as e:
                logger.error(f"Failed to connect to Redis: {e}")
                raise
        return self._redis_client
    
    def _generate_cache_key(
        self, 
        prefix: str, 
        symbol: str, 
        additional_key: Optional[str] = None
    ) -> str:
        """Generate cache key for data."""
        if additional_key:
            return f"{prefix}:{symbol}:{additional_key}"
        return f"{prefix}:{symbol}"
    
    async def set_current_price(
        self, 
        symbol: str, 
        price_data: MarketDataResponse
    ) -> None:
        """
        Cache current price for a symbol.
        
        Args:
            symbol (str): Stock symbol
            price_data (MarketDataResponse): Price data to cache
        """
        try:
            redis = await self._get_redis_client()
            cache_key = self._generate_cache_key('current_price', symbol)
            
            # Serialize price data
            serialized_data = json.dumps({
                'symbol': price_data.symbol,
                'price': price_data.price,
                'timestamp': price_data.timestamp.isoformat(),
                'volume': price_data.volume,
                'provider': price_data.provider
            })
            
            # Set cache with TTL
            await redis.setex(
                cache_key, 
                self.cache_ttl, 
                serialized_data
            )
        except (aioredis.RedisError, TypeError, ValueError) as e:
            logger.warning(f"Failed to cache current price for {symbol}: {e}")
    
    async def get_current_price(self, symbol: str) -> Optional[MarketDataResponse]:
        """
        Retrieve cached current price for a symbol.
        
        Args:
            symbol (str): Stock symbol
        
        Returns:
            Optional[MarketDataResponse]: Cached price data or None
        """
        try:
            redis = await self._get_redis_client()
            cache_key = self._generate_cache_key('current_price', symbol)
            
            # Retrieve cached data
            cached_data = await redis.get(cache_key)
            
            if cached_data:
                # Deserialize price data
                data = json.loads(cached_data)
                return MarketDataResponse(
                    symbol=data['symbol'],
                    price=data['price'],
                    timestamp=datetime.fromisoformat(data['timestamp']),
                    volume=data['volume'],
                    provider=data['provider']
                )
            
            return None
        except (aioredis.RedisError, json.JSONDecodeError, KeyError) as e:
            logger.warning(f"Failed to retrieve cached current price for {symbol}: {e}")
            return None
    
    async def set_historical_data(
        self, 
        symbol: str, 
        historical_data: List[MarketDataResponse],
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> None:
        """Cache historical market data for a symbol."""
        try:
            redis = await self._get_redis_client()
            
            # Generate cache key with date range
            date_key = f"{start_date.date() if start_date else 'default'}_{end_date.date() if end_date else 'default'}"
            cache_key = self._generate_cache_key('historical_data', symbol, date_key)
            
            # Serialize and cache data
            data = [item.dict() for item in historical_data]
            await redis.setex(cache_key, 86400, json.dumps(data))  # 24 hour TTL
            
            logger.info(f"Cached historical data for {symbol}")
            
        except (aioredis.RedisError, TypeError, ValueError) as e:
            logger.warning(f"Failed to cache historical data for {symbol}: {e}")
    
    async def get_historical_data(
        self, 
        symbol: str, 
        start_date: Optional[datetime] = None, 
        end_date: Optional[datetime] = None
    ) -> Optional[List[MarketDataResponse]]:
        """
        Retrieve cached historical market data for a symbol.
        
        Args:
            symbol (str): Stock symbol
            start_date (datetime, optional): Start of historical data range
            end_date (datetime, optional): End of historical data range
        
        Returns:
            Optional[List[MarketDataResponse]]: Cached historical data or None
        """
        try:
            redis = await self._get_redis_client()
            
            # Generate cache key with date range
            date_key = f"{start_date.date() if start_date else 'default'}_{end_date.date() if end_date else 'default'}"
            cache_key = self._generate_cache_key('historical_data', symbol, date_key)
            
            # Retrieve cached data
            cached_data = await redis.get(cache_key)
            
            if cached_data:
                # Deserialize historical data
                data = json.loads(cached_data)
                return [
                    MarketDataResponse(
                        symbol=item['symbol'],
                        price=item['price'],
                        timestamp=datetime.fromisoformat(item['timestamp']),
                        volume=item['volume'],
                        provider=item['provider']
                    ) for item in data
                ]
            
            return None
        except (aioredis.RedisError, json.JSONDecodeError, KeyError) as e:
            logger.warning(f"Failed to retrieve cached historical data for {symbol}: {e}")
            return None
    
    async def clear_cache(self, symbol: Optional[str] = None) -> None:
        """Clear cache for a specific symbol or all symbols."""
        try:
            redis = await self._get_redis_client()
            
            if symbol:
                # Clear cache for specific symbol
                pattern = f"*:{symbol}:*"
                keys = await redis.keys(pattern)
                if keys:
                    await redis.delete(*keys)
                    logger.info(f"Cleared cache for {symbol}")
            else:
                # Clear all cache
                await redis.flushdb()
                logger.info("Cleared all cache")
                
        except (aioredis.RedisError, TypeError, ValueError) as e:
            logger.error(f"Failed to clear cache: {e}")

    async def warm_cache_for_symbols(
        self,
        symbols: List[str],
        days: int = 30,
        ttl: int = 86400,  # 24 hours for historical data
        batch_size: int = 10  # Process symbols in batches to avoid overwhelming providers
    ) -> Dict[str, bool]:
        """
        Pre-load cache for multiple symbols with historical data.
        
        Args:
            symbols: List of symbols to warm cache for
            days: Number of days of historical data to load
            ttl: Cache time-to-live in seconds
            batch_size: Number of symbols to process concurrently
            
        Returns:
            Dictionary mapping symbols to success status
        """
        results = {}
        redis = await self._get_redis_client()
        
        # Process symbols in batches to avoid overwhelming data providers
        for i in range(0, len(symbols), batch_size):
            batch = symbols[i:i + batch_size]
            batch_tasks = [
                self._warm_single_symbol(redis, symbol, days, ttl)
                for symbol in batch
            ]
            
            # Execute batch concurrently with timeout
            try:
                batch_results = await asyncio.wait_for(
                    asyncio.gather(*batch_tasks, return_exceptions=True),
                    timeout=300  # 5 minutes per batch
                )
                
                for symbol, result in zip(batch, batch_results):
                    if isinstance(result, Exception):
                        results[symbol] = False
                        logger.error(f"Failed to warm cache for {symbol}: {result}")
                    else:
                        results[symbol] = result
                        
            except asyncio.TimeoutError:
                logger.error(f"Batch timeout for symbols: {batch}")
                for symbol in batch:
                    results[symbol] = False
                    
            # Brief pause between batches to be nice to data providers
            if i + batch_size < len(symbols):
                await asyncio.sleep(1)
                
        success_count = sum(1 for success in results.values() if success)
        logger.info(f"Cache warming completed: {success_count}/{len(symbols)} symbols successful")
        
        return results

    async def _warm_single_symbol(
        self, 
        redis, 
        symbol: str, 
        days: int, 
        ttl: int
    ) -> bool:
        """
        Warm cache for a single symbol with error handling and metrics.
        """
        start_time = datetime.now()
        
        try:
            # Check if already cached and fresh
            cache_key = self._generate_cache_key('historical_data', symbol, f"{days}d")
            existing = await redis.get(cache_key)
            if existing:
                logger.debug(f"Cache already warm for {symbol}, skipping")
                return True
            
            # Fetch historical data from providers
            historical_data = await self._fetch_historical_data(symbol, days)
            
            if historical_data and len(historical_data) > 0:
                # Store in cache with extended TTL
                serialized_data = json.dumps([
                    item.dict() if hasattr(item, 'dict') else item 
                    for item in historical_data
                ])
                
                await redis.setex(cache_key, ttl, serialized_data)
                
                # Also cache some common derived data
                await self._cache_derived_data(redis, symbol, historical_data, ttl)
                
                duration = (datetime.now() - start_time).total_seconds()
                logger.info(f"Successfully warmed cache for {symbol} in {duration:.2f}s")
                return True
            
            logger.warning(f"No historical data available for {symbol}")
            return False
                
        except Exception as e:
            duration = (datetime.now() - start_time).total_seconds()
            logger.error(f"Failed to warm cache for {symbol} after {duration:.2f}s: {e}")
            return False

    async def _cache_derived_data(
        self, 
        redis, 
        symbol: str, 
        historical_data: List, 
        ttl: int
    ):
        """
        Cache commonly requested derived data to improve cache hit rate.
        """
        try:
            # Cache recent data (last 7 days, 1 day, etc.)
            recent_periods = [1, 7, 30]
            
            for period in recent_periods:
                if len(historical_data) >= period:
                    recent_data = historical_data[-period:]
                    cache_key = self._generate_cache_key('historical_data', symbol, f"{period}d")
                    serialized_data = json.dumps([
                        item.dict() if hasattr(item, 'dict') else item 
                        for item in recent_data
                    ])
                    await redis.setex(cache_key, ttl, serialized_data)
                    
        except (aioredis.RedisError, TypeError, ValueError) as e:
            logger.warning(f"Failed to cache derived data for {symbol}: {e}")

    async def _fetch_historical_data(self, symbol: str, days: int) -> List:
        """
        Helper method to fetch historical data from providers with fallback.
        """
        try:
            from src.api.data.providers.data_source_manager import DataSourceManager
            manager = DataSourceManager()
            
            # Try to fetch data with retry logic
            for attempt in range(3):
                try:
                    data = await manager.fetch_historical_data(symbol, days=days)
                    if data:
                        return data
                except Exception as e:
                    if attempt == 2:  # Last attempt
                        raise e
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
                    
            return []
            
        except Exception as e:
            logger.error(f"Failed to fetch historical data for {symbol}: {e}")
            return []

    async def get_cache_stats(self) -> Dict[str, int]:
        """
        Get cache statistics for monitoring.
        """
        redis = await self._get_redis_client()
        
        try:
            info = await redis.info('memory')
            stats = {
                'used_memory': info.get('used_memory', 0),
                'used_memory_human': info.get('used_memory_human', '0B'),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
            }
            
            # Calculate hit rate
            hits = stats['keyspace_hits']
            misses = stats['keyspace_misses']
            total = hits + misses
            
            stats['hit_rate'] = (hits / total * 100) if total > 0 else 0
            
            return stats
            
        except (aioredis.RedisError, TypeError, ValueError) as e:
            logger.error(f"Failed to get cache stats: {e}")
            return {}

async def warm_top_symbols_cache(cache: MarketDataCache) -> Dict[str, bool]:
    """
    Convenience function to warm cache for top 50 symbols.
    This will be called by the scheduled job.
    """
    logger.info("Starting cache warming for top 50 symbols")
    start_time = datetime.now()
    
    results = await cache.warm_cache_for_symbols(
        symbols=TOP_SYMBOLS,
        days=30,  # Load 30 days of historical data
        ttl=86400,  # Cache for 24 hours
        batch_size=10  # Process 10 symbols concurrently
    )
    
    duration = (datetime.now() - start_time).total_seconds()
    success_count = sum(1 for success in results.values() if success)
    
    logger.info(
        f"Cache warming completed in {duration:.2f}s: "
        f"{success_count}/{len(TOP_SYMBOLS)} symbols successful"
    )
    
    return results

# Global cache instance
market_data_cache = MarketDataCache()