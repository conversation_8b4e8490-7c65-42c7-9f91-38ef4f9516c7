"""
Data Source Configuration Module

Extracted from data_source_manager.py for better modularity.
Handles configuration management for data sources.
"""

import os
import logging
from typing import Dict, Any, Optional
from enum import Enum
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


class DataStatus(Enum):
    """Data status enumeration"""
    SUCCESS = "success"
    FALLBACK = "fallback"
    ERROR = "error"
    PARTIAL = "partial"
    CACHED = "cached"


@dataclass
class DataQualityMetrics:
    """Data quality assessment metrics"""
    completeness: float  # 0.0 to 1.0
    freshness: float     # 0.0 to 1.0
    accuracy: float      # 0.0 to 1.0
    consistency: float   # 0.0 to 1.0
    source_reliability: float  # 0.0 to 1.0
    
    def overall_score(self, weights: Optional[list] = None) -> float:
        """Calculate weighted overall quality score"""
        if weights is None:
            weights = [0.25, 0.2, 0.25, 0.15, 0.15]  # Default weights
        scores = [self.completeness, self.freshness, self.accuracy, 
                 self.consistency, self.source_reliability]
        return sum(w * s for w, s in zip(weights, scores))


@dataclass
class ProviderMetrics:
    """Provider performance and reliability metrics"""
    success_rate: float
    avg_response_time: float
    last_success: Optional[datetime]
    error_count: int
    total_requests: int
    
    def is_healthy(self) -> bool:
        """Check if provider is healthy based on metrics"""
        return (self.success_rate > 0.8 and 
                self.avg_response_time < 5.0 and
                self.error_count < 10)


class DataSourceConfig:
    """Configuration management for data sources"""
    
    def __init__(self):
        self.load_config()
    
    def load_config(self):
        """Load configuration from environment variables"""
        # API Keys
        self.polygon_api_key = os.getenv('POLYGON_API_KEY')
        self.finnhub_api_key = os.getenv('FINNHUB_API_KEY')
        self.alpha_vantage_api_key = os.getenv('ALPHA_VANTAGE_API_KEY')
        self.yahoo_finance_api_key = os.getenv('YAHOO_FINANCE_API_KEY')
        
        # Rate limits (requests per minute)
        self.polygon_rate_limit = int(os.getenv('POLYGON_RATE_LIMIT', '5'))
        self.finnhub_rate_limit = int(os.getenv('FINNHUB_RATE_LIMIT', '60'))
        self.alpha_vantage_rate_limit = int(os.getenv('ALPHA_VANTAGE_RATE_LIMIT', '5'))
        self.yahoo_finance_rate_limit = int(os.getenv('YAHOO_FINANCE_RATE_LIMIT', '100'))
        
        # Timeout settings
        self.request_timeout = float(os.getenv('REQUEST_TIMEOUT', '30.0'))
        self.max_retries = int(os.getenv('MAX_RETRIES', '3'))
        
        # Quality thresholds
        self.min_quality_score = float(os.getenv('MIN_QUALITY_SCORE', '0.7'))
        self.max_data_age_minutes = int(os.getenv('MAX_DATA_AGE_MINUTES', '15'))
        
        # Provider priorities (lower number = higher priority)
        self.provider_priorities = {
            'polygon': int(os.getenv('POLYGON_PRIORITY', '1')),
            'finnhub': int(os.getenv('FINNHUB_PRIORITY', '2')),
            'alpha_vantage': int(os.getenv('ALPHA_VANTAGE_PRIORITY', '3')),
            'yahoo_finance': int(os.getenv('YAHOO_FINANCE_PRIORITY', '4'))
        }
        
        # Fallback settings
        self.enable_fallback = os.getenv('ENABLE_FALLBACK', 'true').lower() == 'true'
        self.max_fallback_attempts = int(os.getenv('MAX_FALLBACK_ATTEMPTS', '3'))
        
        # Audit settings
        self.enable_audit = os.getenv('ENABLE_AUDIT', 'true').lower() == 'true'
        self.audit_level = os.getenv('AUDIT_LEVEL', 'INFO')
        
        # Cache settings
        self.enable_cache = os.getenv('ENABLE_CACHE', 'true').lower() == 'true'
        self.cache_ttl_minutes = int(os.getenv('CACHE_TTL_MINUTES', '5'))
        
        # Concurrency settings
        self.max_concurrent_requests = int(os.getenv('MAX_CONCURRENT_REQUESTS', '10'))
        self.max_concurrent_provider_calls = int(os.getenv('MAX_CONCURRENT_PROVIDER_CALLS', '3'))
        
        logger.info("✅ Data source configuration loaded successfully")
    
    def get_provider_config(self, provider_name: str) -> Dict[str, Any]:
        """Get configuration for a specific provider"""
        base_config = {
            'timeout': self.request_timeout,
            'max_retries': self.max_retries,
            'enable_cache': self.enable_cache,
            'cache_ttl': self.cache_ttl_minutes * 60
        }
        
        if provider_name == 'polygon':
            return {
                **base_config,
                'api_key': self.polygon_api_key,
                'rate_limit': self.polygon_rate_limit,
                'priority': self.provider_priorities['polygon']
            }
        elif provider_name == 'finnhub':
            return {
                **base_config,
                'api_key': self.finnhub_api_key,
                'rate_limit': self.finnhub_rate_limit,
                'priority': self.provider_priorities['finnhub']
            }
        elif provider_name == 'alpha_vantage':
            return {
                **base_config,
                'api_key': self.alpha_vantage_api_key,
                'rate_limit': self.alpha_vantage_rate_limit,
                'priority': self.provider_priorities['alpha_vantage']
            }
        elif provider_name == 'yahoo_finance':
            return {
                **base_config,
                'api_key': self.yahoo_finance_api_key,
                'rate_limit': self.yahoo_finance_rate_limit,
                'priority': self.provider_priorities['yahoo_finance']
            }
        else:
            return base_config
    
    def validate_config(self) -> bool:
        """Validate that required configuration is present"""
        required_keys = ['polygon_api_key', 'finnhub_api_key']
        missing_keys = []
        
        for key in required_keys:
            if not getattr(self, key):
                missing_keys.append(key)
        
        if missing_keys:
            logger.warning(f"⚠️ Missing required configuration: {missing_keys}")
            return False
        
        logger.info("✅ Configuration validation passed")
        return True


# Global configuration instance
config = DataSourceConfig()
