"""
Pipeline Auditing Module

Extracted from data_source_manager.py for better modularity.
Handles comprehensive pipeline auditing for security, performance, and compliance.
"""

import logging
import time
import uuid
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from collections import defaultdict

logger = logging.getLogger(__name__)


@dataclass
class AuditEvent:
    """Audit event for comprehensive pipeline monitoring"""
    event_id: str
    timestamp: datetime
    event_type: str
    severity: str  # INFO, WARNING, ERROR, CRITICAL
    details: Dict[str, Any]
    performance_metrics: Dict[str, float]
    user_context: Dict[str, Any]


class PipelineAuditor:
    """Comprehensive pipeline auditor for security, performance, and compliance"""
    
    def __init__(self):
        self.audit_events: List[AuditEvent] = []
        self.performance_metrics: Dict[str, List[float]] = defaultdict(list)
        self.security_events: List[AuditEvent] = []
        self.error_patterns: Dict[str, int] = defaultdict(int)
        
    def log_event(self, event_type: str, severity: str, details: Dict[str, Any], 
                  performance_metrics: Optional[Dict[str, float]] = None,
                  user_context: Optional[Dict[str, Any]] = None):
        """Log an audit event"""
        event = AuditEvent(
            event_id=str(uuid.uuid4()),
            timestamp=datetime.now(),
            event_type=event_type,
            severity=severity,
            details=details,
            performance_metrics=performance_metrics or {},
            user_context=user_context or {}
        )
        
        self.audit_events.append(event)
        
        # Track security events separately
        if severity in ['ERROR', 'CRITICAL'] or 'security' in event_type.lower():
            self.security_events.append(event)
        
        # Track error patterns
        if severity in ['ERROR', 'CRITICAL']:
            error_key = f"{event_type}:{details.get('error_type', 'unknown')}"
            self.error_patterns[error_key] += 1
        
        # Log to standard logger
        log_level = getattr(logging, severity, logging.INFO)
        logger.log(log_level, f"[{event_type}] {details}")
    
    def log_performance(self, operation: str, duration: float, success: bool = True):
        """Log performance metrics"""
        self.performance_metrics[operation].append(duration)
        
        self.log_event(
            event_type="PERFORMANCE",
            severity="INFO" if success else "WARNING",
            details={
                "operation": operation,
                "duration_seconds": duration,
                "success": success
            },
            performance_metrics={"duration": duration}
        )
    
    def log_data_access(self, provider: str, symbol: str, user_id: str, success: bool = True):
        """Log data access for compliance"""
        self.log_event(
            event_type="DATA_ACCESS",
            severity="INFO" if success else "WARNING",
            details={
                "provider": provider,
                "symbol": symbol,
                "success": success
            },
            user_context={"user_id": user_id}
        )
    
    def log_security_event(self, event_type: str, details: Dict[str, Any], user_context: Optional[Dict[str, Any]] = None):
        """Log security-related events"""
        self.log_event(
            event_type=f"SECURITY_{event_type}",
            severity="WARNING",
            details=details,
            user_context=user_context
        )
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary statistics"""
        summary = {}
        
        for operation, durations in self.performance_metrics.items():
            if durations:
                summary[operation] = {
                    "count": len(durations),
                    "avg_duration": sum(durations) / len(durations),
                    "min_duration": min(durations),
                    "max_duration": max(durations),
                    "total_duration": sum(durations)
                }
        
        return summary
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get error pattern summary"""
        total_errors = sum(self.error_patterns.values())
        
        return {
            "total_errors": total_errors,
            "error_patterns": dict(self.error_patterns),
            "most_common_errors": sorted(
                self.error_patterns.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:5]
        }
    
    def get_security_summary(self) -> Dict[str, Any]:
        """Get security events summary"""
        return {
            "total_security_events": len(self.security_events),
            "recent_events": [
                asdict(event) for event in self.security_events[-10:]
            ]
        }
    
    def get_audit_report(self) -> Dict[str, Any]:
        """Generate comprehensive audit report"""
        return {
            "report_timestamp": datetime.now().isoformat(),
            "total_events": len(self.audit_events),
            "performance_summary": self.get_performance_summary(),
            "error_summary": self.get_error_summary(),
            "security_summary": self.get_security_summary(),
            "recent_events": [
                asdict(event) for event in self.audit_events[-20:]
            ]
        }
    
    def cleanup_old_events(self, max_age_hours: int = 24):
        """Clean up old audit events to prevent memory bloat"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        # Keep only recent events
        self.audit_events = [
            event for event in self.audit_events 
            if event.timestamp > cutoff_time
        ]
        
        self.security_events = [
            event for event in self.security_events 
            if event.timestamp > cutoff_time
        ]
        
        logger.info(f"Cleaned up audit events older than {max_age_hours} hours")


# Global auditor instance
auditor = PipelineAuditor()
