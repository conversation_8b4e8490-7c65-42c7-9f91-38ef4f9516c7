"""
Data Source Manager Modules

Modular components extracted from the monolithic data_source_manager.py
for better maintainability and separation of concerns.
"""

from .config import (
    DataSourceConfig,
    DataStatus,
    DataQualityMetrics,
    ProviderMetrics,
    config
)

from .auditing import (
    AuditEvent,
    PipelineAuditor,
    auditor
)

from .rate_limiting import (
    RateLimiter,
    GlobalRateLimitManager,
    rate_limit_manager
)

from .validation import (
    ValidationResult,
    RealDataValidator,
    DataQualityAssessor,
    quality_assessor
)

__all__ = [
    # Config
    'DataSourceConfig',
    'DataStatus',
    'DataQualityMetrics',
    'ProviderMetrics',
    'config',
    
    # Auditing
    'AuditEvent',
    'PipelineAuditor',
    'auditor',
    
    # Rate Limiting
    'RateLimiter',
    'GlobalRateLimitManager',
    'rate_limit_manager',
    
    # Validation
    'ValidationResult',
    'RealDataValidator',
    'DataQualityAssessor',
    'quality_assessor'
]
