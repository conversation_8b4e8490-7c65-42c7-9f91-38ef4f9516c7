"""
Alpha Vantage Market Data Provider
"""
import asyncio
import httpx
import logging
from datetime import datetime, timezone
from typing import List, Optional, Any
from src.api.data.providers.base import BaseMarketDataProvider, MarketDataResponse, ProviderType
from src.shared.error_handling.fallback import MarketDataError, ProviderUnavailableError, RateLimitError

logger = logging.getLogger(__name__)

class AlphaVantageProvider(BaseMarketDataProvider):
    """Alpha Vantage market data provider with rate limiting and error handling."""
    
    def __init__(self):
        super().__init__("Alpha Vantage", ProviderType.ALPHA_VANTAGE)
        self.api_key = os.getenv('ALPHA_VANTAGE_API_KEY', '')
        self.base_url = "https://www.alphavantage.co/query"
        self.session = httpx.AsyncClient(timeout=10.0)
        self.rate_limiter = asyncio.Semaphore(1)  # Free tier: 5 calls/min, conservative limit
        self.is_configured = bool(self.api_key)
        
        if not self.is_configured:
            logger.warning("Alpha Vantage provider not configured - API key missing")
    
    async def get_current_price(self, symbol: str) -> MarketDataResponse:
        """Get current price for a symbol from Alpha Vantage."""
        if not self.is_configured:
            raise ProviderUnavailableError("Alpha Vantage API key not configured")
        
        async with self.rate_limiter:
            start_time = asyncio.get_event_loop().time()
            try:
                params = {
                    'function': 'GLOBAL_QUOTE',
                    'symbol': symbol.upper(),
                    'apikey': self.api_key
                }
                response = await self.session.get(self.base_url, params=params)
                response.raise_for_status()
                data = response.json()
                
                if 'Global Quote' in data and data['Global Quote']:
                    quote = data['Global Quote']
                    price = float(quote['05. price'])
                    latest_day = quote['07. latest trading day']
                    timestamp = datetime.fromisoformat(latest_day.replace(' ', 'T') + ':00Z').replace(tzinfo=timezone.utc)
                    
                    response_time = (asyncio.get_event_loop().time() - start_time) * 1000
                    metadata = self._create_metadata(response_time_ms=response_time)
                    
                    return self._create_response(
                        symbol, price, timestamp,
                        volume=float(quote.get('06. volume', 0)),
                        change_percent=float(quote.get('10. change percent', 0)),
                        metadata=metadata
                    )
                else:
                    error_msg = data.get('Note', 'Unknown error') if 'Note' in data else 'No data returned'
                    raise MarketDataError(f"Alpha Vantage error for {symbol}: {error_msg}")
                    
            except httpx.RateLimitExceeded:
                raise RateLimitError("Alpha Vantage rate limit exceeded")
            except Exception as e:
                logger.error(f"Alpha Vantage API error for {symbol}: {e}")
                raise MarketDataError(f"Failed to fetch data from Alpha Vantage for {symbol}: {str(e)}")
    
    async def get_historical_data(
        self, 
        symbol: str, 
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        days: Optional[int] = None
    ) -> List[MarketDataResponse]:
        """Get historical data for a symbol from Alpha Vantage."""
        if not self.is_configured:
            raise ProviderUnavailableError("Alpha Vantage API key not configured")
        
        if days is None:
            if start_date and end_date:
                days = (end_date - start_date).days + 1
            else:
                days = 100  # Default to ~3 months of daily data
        
        async with self.rate_limiter:
            start_time = asyncio.get_event_loop().time()
            try:
                # Use TIME_SERIES_DAILY_ADJUSTED for daily historical data
                params = {
                    'function': 'TIME_SERIES_DAILY_ADJUSTED',
                    'symbol': symbol.upper(),
                    'outputsize': 'compact' if days <= 100 else 'full',
                    'apikey': self.api_key
                }
                response = await self.session.get(self.base_url, params=params)
                response.raise_for_status()
                data = response.json()
                
                if 'Time Series (Daily)' not in data:
                    error_msg = data.get('Note', 'Unknown error') if 'Note' in data else 'No data returned'
                    raise MarketDataError(f"Alpha Vantage historical error for {symbol}: {error_msg}")
                
                time_series = data['Time Series (Daily)']
                historical_data = []
                
                for date_str, daily_data in list(time_series.items())[-days:]:  # Last N days
                    date = datetime.strptime(date_str, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                    price = float(daily_data['5. adjusted close'])
                    open_price = float(daily_data['1. open'])
                    high = float(daily_data['2. high'])
                    low = float(daily_data['3. low'])
                    volume = float(daily_data['6. volume'])
                    
                    response_time = (asyncio.get_event_loop().time() - start_time) * 1000
                    metadata = self._create_metadata(
                        response_time_ms=response_time,
                        data_window_start=min(historical_data[0].timestamp if historical_data else date, date),
                        data_window_end=max(historical_data[-1].timestamp if historical_data else date, date) if historical_data else date
                    )
                    
                    md_response = self._create_response(
                        symbol, price, date,
                        open=open_price,
                        high=high,
                        low=low,
                        volume=volume,
                        metadata=metadata
                    )
                    historical_data.append(md_response)
                
                return historical_data
                
            except httpx.RateLimitExceeded:
                raise RateLimitError("Alpha Vantage rate limit exceeded")
            except Exception as e:
                logger.error(f"Alpha Vantage historical API error for {symbol}: {e}")
                raise MarketDataError(f"Failed to fetch historical data from Alpha Vantage for {symbol}: {str(e)}")
    

    async def get_ticker(self, symbol: str) -> dict:
        """Get ticker data for a symbol (alias for get_current_price)."""
        try:
            response = await self.get_current_price(symbol)
            return {
                "symbol": symbol,
                "price": response.data.get("price", 0),
                "change": response.data.get("change", 0),
                "change_percent": response.data.get("change_percent", 0),
                "volume": response.data.get("volume", 0),
                "timestamp": response.data.get("timestamp", "")
            }
        except Exception as e:
            logger.error(f"Error getting ticker data for {symbol}: {e}")
            raise MarketDataError(f"Failed to get ticker data: {e}")

    async def get_history(self, symbol: str, period: str = "1mo", interval: str = "1d") -> dict:
        """Get historical data for a symbol (alias for get_historical_data)."""
        try:
            response = await self.get_historical_data(symbol, period, interval)
            return response.data
        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            raise MarketDataError(f"Failed to get historical data: {e}")
    async def close(self):
        """Close the HTTP session."""
        await self.session.aclose()