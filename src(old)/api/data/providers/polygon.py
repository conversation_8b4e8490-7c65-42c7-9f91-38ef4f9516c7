import os
import asyncio
from datetime import datetime, timedelta, timezone
from typing import List, Optional, Dict, Any

import httpx
from pydantic import BaseModel, Field, ValidationError

# Import unified base classes
from src.shared.data_providers.unified_base import UnifiedDataProvider, HistoricalData, ProviderError
from src.shared.data_providers.unified_base import (
    ProviderType,
    MarketDataResponse
)

from src.shared.error_handling.logging import get_logger
from src.shared.error_handling.fallback import MarketDataError

logger = get_logger(__name__)

class PolygonStockResponse(BaseModel):
    """
    Polygon stock data response model.
    """
    symbol: str = Field(..., alias='ticker')
    price: float = Field(..., alias='last_price')
    timestamp: datetime = Field(..., alias='last_updated')
    volume: Optional[float] = Field(None, alias='volume')
    
    class Config:
        allow_population_by_field_name = True

class PolygonProvider(UnifiedDataProvider):
    """
    Market data provider for Polygon.io.
    
    Supports current price and historical data retrieval.
    Updated to use unified base class for consistency.
    """
    
    def __init__(
        self, 
        api_key: Optional[str] = None, 
        base_url: str = 'https://api.polygon.io/v2', 
        rate_limit: int = 5,
        config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize Polygon market data provider.
        
        Args:
            api_key (str, optional): Polygon API key. Defaults to env var.
            base_url (str, optional): Base API URL. 
            rate_limit (int, optional): Requests per minute. Defaults to 5.
            config (dict, optional): Additional configuration.
        """
        # Store API configuration
        self.api_key = api_key or os.getenv('POLYGON_API_KEY')
        if not self.api_key:
            raise ValueError("Polygon API key is required")
        
        self.base_url = base_url
        
        # Initialize unified base class
        provider_config = config or {}
        provider_config['rate_limit'] = rate_limit
        provider_config['timeout'] = provider_config.get('timeout', 30.0)
        provider_config['max_retries'] = provider_config.get('max_retries', 3)
        
        super().__init__(
            provider_name="polygon",
            provider_type=ProviderType.POLYGON,
            config=provider_config
        )
        
        # Validate API key
        self._validate_api_key()
    
    def _validate_api_key(self):
        """
        Validate Polygon API key by checking format and setting up validation flag.
        
        Note: Full validation happens asynchronously on first API call.
        """
        if not self.api_key:
            raise ValueError("Polygon API key is required")
        
        # Check if API key format is valid (basic validation)
        if len(self.api_key) < 10:
            raise ValueError("Invalid API key format")
        
        # Set flag to indicate that async validation is needed on first API call
        self._api_key_validated = False
        
        # Log that initial validation passed
        logger.info("Polygon API key format validated, will perform full validation on first API call")
    
    async def _validate_api_key_async(self):
        """
        Async validation of Polygon API key by checking account status.
        """
        try:
            async with httpx.AsyncClient() as client:
                # Use Polygon's account endpoint to validate API key
                url = f"{self.base_url}/account"
                params = {'apiKey': self.api_key}
                
                response = await client.get(url, params=params)
                
                if response.status_code == 401:
                    raise ValueError("Invalid Polygon API key")
                elif response.status_code == 403:
                    raise ValueError("Polygon API key lacks required permissions")
                elif response.status_code == 429:
                    raise ValueError("Polygon API rate limit exceeded")
                elif response.status_code != 200:
                    raise ValueError(f"Polygon API error: {response.status_code}")
                
                # Parse account info to validate key is working
                account_data = response.json()
                if not account_data.get('account_id'):
                    raise ValueError("Invalid response from Polygon API")
                
                logger.info(f"Polygon API key validated successfully for account: {account_data.get('account_id')}")
                return True
                
        except httpx.RequestError as e:
            logger.warning(f"Could not validate Polygon API key (network error): {e}")
            # Don't fail completely on network errors, just log warning
            return False
        except Exception as e:
            logger.error(f"Polygon API key validation failed: {e}")
            raise ValueError(f"Failed to validate Polygon API key: {e}")
    
    async def get_current_price(self, symbol: str) -> MarketDataResponse:
        """Get current price for a symbol (required by UnifiedDataProvider)."""
        try:
            # Use the existing _get_current_price method
            price_data = await self._get_current_price(symbol)
            
            # Create metadata
            metadata = self._create_metadata()
            
            # Create response
            return self._create_response(
                symbol=symbol,
                price=price_data.get('price', 0.0),
                timestamp=price_data.get('timestamp', datetime.now(timezone.utc)),
                volume=price_data.get('volume'),
                change=price_data.get('change'),
                change_percent=price_data.get('change_percent'),
                open=price_data.get('open'),
                high=price_data.get('high'),
                low=price_data.get('low'),
                previous_close=price_data.get('previous_close'),
                metadata=metadata
            )
            
        except Exception as e:
            if isinstance(e, ProviderError):
                raise
            raise ProviderError(f"Failed to get current price: {str(e)}", self.provider_name)
    
    async def get_historical_data(
        self,
        symbol: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        days: Optional[int] = None
    ) -> HistoricalData:
        """Get historical data for a symbol (required by UnifiedDataProvider)."""
        try:
            # Use the existing _get_historical_data method
            hist_data = await self._get_historical_data(symbol, start_date, end_date, days)
            
            # Create metadata
            metadata = self._create_metadata(
                data_window_start=start_date,
                data_window_end=end_date
            )
            
            # Create historical data response
            return HistoricalData(
                symbol=symbol,
                dates=hist_data.get('dates', []),
                opens=hist_data.get('opens', []),
                closes=hist_data.get('closes', []),
                highs=hist_data.get('highs', []),
                lows=hist_data.get('lows', []),
                volumes=hist_data.get('volumes', []),
                metadata=metadata,
                additional_fields=hist_data.get('additional_fields')
            )
            
        except Exception as e:
            if isinstance(e, ProviderError):
                raise
            raise ProviderError(f"Failed to get historical data: {str(e)}", self.provider_name)
    
    async def get_stock_data(self, symbol: str) -> Dict[str, Any]:
        """Get comprehensive stock data (compatibility method)."""
        try:
            current_price = await self.get_current_price(symbol)
            return {
                'symbol': symbol,
                'current_price': current_price.price,
                'timestamp': current_price.timestamp.isoformat(),
                'volume': current_price.volume,
                'change': current_price.change,
                'change_percent': current_price.change_percent,
                'provider': self.provider_name,
                'metadata': current_price.metadata
            }
        except Exception as e:
            logger.error(f"Error getting stock data for {symbol}: {e}")
            raise
    
    async def _get_current_price(self, symbol: str) -> Dict[str, Any]:
        """Helper method to get current price data."""
        try:
            # Validate symbol
            symbol = symbol.upper()
            if not self._validate_symbol(symbol):
                raise ValueError(f"Invalid symbol: {symbol}")
            
            # Validate API key on first use or if previous validation failed
            if not getattr(self, '_api_key_validated', False):
                validation_result = await self._validate_api_key_async()
                if not validation_result:
                    raise ProviderError("API key validation failed", self.provider_name)
                self._api_key_validated = True
            
            # Use rate limiter from unified base
            if not await self.rate_limiter.acquire():
                raise ProviderRateLimitError("Rate limit exceeded", self.provider_name)
            
            async with httpx.AsyncClient() as client:
                # Polygon's latest stock price endpoint
                url = f"{self.base_url}/aggs/ticker/{symbol}/prev"
                params = {
                    'apiKey': self.api_key,
                    'adjusted': 'true'
                }
                
                response = await client.get(url, params=params)
                response.raise_for_status()
                
                data = response.json()
                
                # Extract the most recent data point
                if data.get('results') and len(data['results']) > 0:
                    stock_data = data['results'][0]
                    
                    return {
                        'price': stock_data.get('c', 0),  # Close price
                        'timestamp': datetime.fromtimestamp(stock_data.get('t', 0) / 1000, tz=timezone.utc),
                        'volume': stock_data.get('v', 0),
                        'change': 0,  # Not available in this endpoint
                        'change_percent': 0,
                        'open': stock_data.get('o', 0),
                        'high': stock_data.get('h', 0),
                        'low': stock_data.get('l', 0),
                        'previous_close': stock_data.get('pc', 0)
                    }
                
                raise ProviderError(f"No data available for {symbol}", self.provider_name)
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error retrieving {symbol} price: {e}")
            raise ProviderError(f"Failed to retrieve price for {symbol}", self.provider_name)
        except (ValueError, ValidationError) as e:
            logger.error(f"Data validation error for {symbol}: {e}")
            raise ProviderError(f"Invalid data for {symbol}", self.provider_name)
    
    async def _get_historical_data(
        self,
        symbol: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        days: Optional[int] = None
    ) -> Dict[str, Any]:
        """Helper method to get historical data."""
        try:
            # Handle date parameters
            if start_date is not None and end_date is not None and days is None:
                # Called with (symbol, start_date, end_date) - use as is
                pass
            elif days is not None:
                # Called with days parameter
                end_date = datetime.now(timezone.utc)
                start_date = end_date - timedelta(days=days)
            else:
                # Default to last 30 days if no dates provided
                end_date = end_date or datetime.now(timezone.utc)
                start_date = start_date or (end_date - timedelta(days=30))
            
            # Validate symbol
            symbol = symbol.upper()
            if not self._validate_symbol(symbol):
                raise ValueError(f"Invalid symbol: {symbol}")
            
            # Validate API key on first use or if previous validation failed
            if not getattr(self, '_api_key_validated', False):
                validation_result = await self._validate_api_key_async()
                if not validation_result:
                    raise ProviderError("API key validation failed", self.provider_name)
                self._api_key_validated = True
            
            # Use rate limiter from unified base
            if not await self.rate_limiter.acquire():
                raise ProviderRateLimitError("Rate limit exceeded", self.provider_name)
            
            async with httpx.AsyncClient() as client:
                # Polygon's aggregates endpoint
                url = f"{self.base_url}/aggs/ticker/{symbol}/range/1/day/{start_date.date()}/{end_date.date()}"
                params = {
                    'apiKey': self.api_key,
                    'adjusted': 'true'
                }
                
                response = await client.get(url, params=params)
                response.raise_for_status()
                
                data = response.json()
                
                # Extract historical data
                dates = []
                opens = []
                closes = []
                highs = []
                lows = []
                volumes = []
                
                if data.get('results'):
                    for stock_data in data['results']:
                        dates.append(datetime.fromtimestamp(stock_data.get('t', 0) / 1000, tz=timezone.utc))
                        opens.append(stock_data.get('o', 0))
                        closes.append(stock_data.get('c', 0))
                        highs.append(stock_data.get('h', 0))
                        lows.append(stock_data.get('l', 0))
                        volumes.append(stock_data.get('v', 0))
                
                return {
                    'dates': dates,
                    'opens': opens,
                    'closes': closes,
                    'highs': highs,
                    'lows': lows,
                    'volumes': volumes,
                    'additional_fields': {}
                }
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error retrieving historical data for {symbol}: {e}")
            raise ProviderError(f"Failed to retrieve historical data for {symbol}", self.provider_name)
        except (ValueError, ValidationError) as e:
            logger.error(f"Data validation error for {symbol}: {e}")
            raise ProviderError(f"Invalid historical data for {symbol}", self.provider_name)
    
    def _validate_symbol(self, symbol: str) -> bool:
        """
        Validate stock symbol format.
        
        Args:
            symbol (str): Stock ticker symbol
        
        Returns:
            bool: Whether symbol is valid
        """
        # Basic symbol validation
        if not symbol or not isinstance(symbol, str):
            return False
            
        # Check length and content
        if 1 <= len(symbol) <= 10:
            # Allow alphanumeric symbols with dots (e.g., BRK.A)
            return bool(all(c.isalnum() or c == '.' for c in symbol))
            
        return False