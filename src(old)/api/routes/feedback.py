from fastapi import APIRouter, HTTPException, Body
from typing import Dict, Any

from src.core.feedback_mechanism import response_feedback_collector
from src.api.schemas.feedback_schema import FeedbackSubmission, FeedbackReport

feedback_router = APIRouter(prefix="/feedback", tags=["Feedback"])

@feedback_router.post("/submit")
async def submit_feedback(
    feedback: FeedbackSubmission = Body(...)
):
    """
    Submit user feedback for a specific response
    
    Args:
        feedback (FeedbackSubmission): Feedback details
    
    Returns:
        dict: Feedback submission result
    """
    try:
        # Validate and record feedback
        feedback_id = response_feedback_collector.record_feedback(
            response=feedback.response,
            rating=feedback.rating,
            user_comment=feedback.comment,
            feedback_type=feedback.feedback_type
        )
        
        return {
            "status": "success", 
            "feedback_id": feedback_id,
            "message": "Feedback recorded successfully"
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error submitting feedback: {str(e)}")

@feedback_router.get("/report", response_model=FeedbackReport)
async def get_feedback_report():
    """
    Get comprehensive feedback report
    
    Returns:
        FeedbackReport: Detailed feedback analysis
    """
    try:
        report = response_feedback_collector.get_feedback_report()
        return FeedbackReport(**report)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving feedback report: {str(e)}")

@feedback_router.get("/detailed-analysis")
async def get_detailed_feedback_analysis(days: int = 30):
    """
    Get detailed feedback analysis for a specific time period
    
    Args:
        days (int, optional): Number of days to analyze. Defaults to 30.
    
    Returns:
        dict: Detailed feedback analysis
    """
    try:
        analysis = response_feedback_collector.get_detailed_analysis(days)
        return analysis
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving detailed analysis: {str(e)}") 