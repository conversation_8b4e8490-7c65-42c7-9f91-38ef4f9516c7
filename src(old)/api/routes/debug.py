from fastapi import APIRouter, Request, BackgroundTasks
from fastapi.responses import StreamingResponse, JSONResponse
from typing import Dict, Any
import asyncio
import json

router = APIRouter()

# Single broadcaster queue for in-memory debug events
_debug_queue: asyncio.Queue[Dict[str, Any]] = asyncio.Queue()

async def _event_generator(request: Request):
    """Yield Server-Sent Events from the internal queue until client disconnects."""
    while True:
        # If client disconnected, stop the generator
        if await request.is_disconnected():
            break
        try:
            event = await _debug_queue.get()
        except asyncio.CancelledError:
            break

        payload = json.dumps(event)
        yield f"event: message\ndata: {payload}\n\n"

@router.get("/stream")
async def stream_events(request: Request):
    """SSE stream of debug events. Connect from the client to receive live logs/commands."""
    return StreamingResponse(_event_generator(request), media_type="text/event-stream")

@router.post("/command")
async def post_command(payload: Dict[str, Any]):
    """Post a debug/command event to the console. Payload should contain at least `command`."""
    event = {
        "type": "command",
        "command": payload.get("command"),
        "meta": payload.get("meta", {}),
        "timestamp": asyncio.get_event_loop().time()
    }
    await _debug_queue.put(event)
    return JSONResponse({"status": "ok"})

@router.post("/tool")
async def report_tool(payload: Dict[str, Any]):
    """Report a tool/provider used during pipeline execution."""
    event = {
        "type": "tool",
        "tool": payload.get("tool"),
        "details": payload.get("details", {}),
        "timestamp": asyncio.get_event_loop().time()
    }
    await _debug_queue.put(event)
    return JSONResponse({"status": "ok"})

@router.post("/log")
async def push_log(payload: Dict[str, Any]):
    """Push an arbitrary log line into the debug console."""
    event = {
        "type": "log",
        "level": payload.get("level", "info"),
        "message": payload.get("message"),
        "meta": payload.get("meta", {}),
        "timestamp": asyncio.get_event_loop().time()
    }
    await _debug_queue.put(event)
    return JSONResponse({"status": "ok"}) 