from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse
from typing import Dict, Any
import json
from datetime import datetime

from src.shared.error_handling.logging import get_logger
from .bot_health import bot_health_monitor, detailed_health_check, system_metrics

logger = get_logger(__name__)
router = APIRouter()

@router.get("/dashboard", response_class=HTMLResponse, tags=["Dashboard"])
async def health_dashboard():
    """Render a comprehensive health dashboard as HTML"""
    
    try:
        # Get health data
        detailed_health = await detailed_health_check()
        metrics = await system_metrics()
        
        # Create dashboard HTML
        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Bot Health Dashboard</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }}
        
        .header {{
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }}
        
        .status-badge {{
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
        }}
        
        .status-healthy {{ background: #d4edda; color: #155724; }}
        .status-degraded {{ background: #fff3cd; color: #856404; }}
        .status-unhealthy {{ background: #f8d7da; color: #721c24; }}
        
        .grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        
        .card {{
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        
        .card h3 {{
            margin-bottom: 15px;
            color: #2c3e50;
            font-size: 18px;
        }}
        
        .metric {{
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }}
        
        .metric:last-child {{
            border-bottom: none;
        }}
        
        .metric-value {{
            font-weight: 600;
            color: #3498db;
        }}
        
        .progress-bar {{
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }}
        
        .progress-fill {{
            height: 100%;
            background: #3498db;
            transition: width 0.3s ease;
        }}
        
        .alert {{
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }}
        
        .alert-warning {{
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }}
        
        .alert-danger {{
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }}
        
        .refresh-btn {{
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-bottom: 20px;
        }}
        
        .refresh-btn:hover {{
            background: #2980b9;
        }}
        
        .auto-refresh {{
            margin-bottom: 20px;
        }}
        
        .timestamp {{
            text-align: center;
            color: #7f8c8d;
            font-size: 14px;
            margin-top: 20px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Trading Bot Health Dashboard</h1>
            <p>Real-time monitoring of bot performance and system health</p>
            
            <div style="margin-top: 15px;">
                <span class="status-badge status-{detailed_health['overall_status']}">
                    Overall: {detailed_health['overall_status'].upper()}
                </span>
                <span style="margin-left: 10px; font-size: 14px; color: #7f8c8d;">
                    Last updated: <span id="lastUpdate">{datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC</span>
                </span>
            </div>
        </div>
        
        <div class="auto-refresh">
            <label>
                <input type="checkbox" id="autoRefresh" checked> Auto-refresh every 30 seconds
            </label>
            <button class="refresh-btn" onclick="refreshData()">🔄 Refresh Now</button>
        </div>
        
        <div class="grid">
            <!-- Bot Status -->
            <div class="card">
                <h3>🤖 Bot Status</h3>
                <div class="metric">
                    <span>Connection Status:</span>
                    <span class="metric-value" id="botStatus">{detailed_health['services']['bot']}</span>
                </div>
                <div class="metric">
                    <span>Discord Guilds:</span>
                    <span class="metric-value">{detailed_health['details']['bot']['bot']['guilds']}</span>
                </div>
                <div class="metric">
                    <span>Commands Registered:</span>
                    <span class="metric-value">{detailed_health['details']['bot']['bot']['commands_registered']}</span>
                </div>
                <div class="metric">
                    <span>Process Uptime:</span>
                    <span class="metric-value">{format_uptime(detailed_health['details']['bot']['uptime']['process'])}</span>
                </div>
            </div>
            
            <!-- System Resources -->
            <div class="card">
                <h3>💻 System Resources</h3>
                <div class="metric">
                    <span>CPU Usage:</span>
                    <span class="metric-value">{metrics['cpu']['percent']:.1f}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {min(metrics['cpu']['percent'], 100)}%"></div>
                </div>
                
                <div class="metric">
                    <span>Memory Usage:</span>
                    <span class="metric-value">{metrics['memory']['percent']:.1f}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {min(metrics['memory']['percent'], 100)}%"></div>
                </div>
                
                <div class="metric">
                    <span>Disk Usage:</span>
                    <span class="metric-value">{metrics['disk']['percent']:.1f}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {min(metrics['disk']['percent'], 100)}%"></div>
                </div>
            </div>
            
            <!-- Database Status -->
            <div class="card">
                <h3>🗄️ Database</h3>
                <div class="metric">
                    <span>Status:</span>
                    <span class="status-badge status-{detailed_health['services']['database']}">
                        {detailed_health['services']['database'].upper()}
                    </span>
                </div>
                {generate_database_details(detailed_health)}
            </div>
            
            <!-- Services Status -->
            <div class="card">
                <h3>🔧 Services</h3>
                {"".join(generate_service_status(detailed_health['services']))}
            </div>
            
            <!-- Network -->
            <div class="card">
                <h3>🌐 Network</h3>
                <div class="metric">
                    <span>Bytes Sent:</span>
                    <span class="metric-value">{format_bytes(metrics['network']['bytes_sent'])}</span>
                </div>
                <div class="metric">
                    <span>Bytes Received:</span>
                    <span class="metric-value">{format_bytes(metrics['network']['bytes_recv'])}</span>
                </div>
                <div class="metric">
                    <span>Connections:</span>
                    <span class="metric-value">{metrics['process']['connections']}</span>
                </div>
            </div>
            
            <!-- Alerts -->
            <div class="card">
                <h3>⚠️ Alerts</h3>
                {generate_alerts(detailed_health)}
            </div>
        </div>
        
        <div class="timestamp">
            Dashboard refreshed at {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC
        </div>
    </div>
    
    <script>
        let autoRefreshInterval;
        
        function refreshData() {{
            location.reload();
        }}
        
        function startAutoRefresh() {{
            if (document.getElementById('autoRefresh').checked) {{
                autoRefreshInterval = setInterval(refreshData, 30000);
            }}
        }}
        
        function stopAutoRefresh() {{
            if (autoRefreshInterval) {{
                clearInterval(autoRefreshInterval);
            }}
        }}
        
        document.getElementById('autoRefresh').addEventListener('change', function() {{
            if (this.checked) {{
                startAutoRefresh();
            }} else {{
                stopAutoRefresh();
            }}
        }});
        
        // Start auto-refresh on page load
        startAutoRefresh();
        
        // Update timestamp every second
        setInterval(() => {{
            document.getElementById('lastUpdate').textContent = new Date().toISOString().slice(0, 19).replace('T', ' ') + ' UTC';
        }}, 1000);
    </script>
</body>
</html>
        """
        
        return html_content
        
    except Exception as e:
        logger.error(f"Failed to generate dashboard: {e}")
        return f"""
        <html>
        <body>
            <h1>Error Loading Dashboard</h1>
            <p>Failed to load health data: {str(e)}</p>
            <p><a href="/dashboard">Try refreshing</a></p>
        </body>
        </html>
        """

def format_uptime(seconds):
    """Format uptime in human-readable format"""
    if seconds < 60:
        return f"{int(seconds)}s"
    elif seconds < 3600:
        return f"{int(seconds/60)}m"
    elif seconds < 86400:
        return f"{int(seconds/3600)}h {int((seconds%3600)/60)}m"
    else:
        days = int(seconds/86400)
        hours = int((seconds%86400)/3600)
        return f"{days}d {hours}h"

def format_bytes(bytes_val):
    """Format bytes in human-readable format"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes_val < 1024:
            return f"{bytes_val:.1f} {unit}"
        bytes_val /= 1024
    return f"{bytes_val:.1f} PB"

def generate_database_details(health_data):
    """Generate database details HTML"""
    db_data = health_data['details']['system']['database']
    if db_data['error']:
        return f'<div class="alert alert-danger">Database Error: {db_data["error"]}</div>'
    return '<div class="alert alert-success">Database connection healthy</div>'

def generate_service_status(services):
    """Generate service status HTML"""
    html_parts = []
    for service, status in services.items():
        badge_class = f'status-{status}'
        html_parts.append(f'''
            <div class="metric">
                <span>{service.title()}:</span>
                <span class="status-badge {badge_class}">{status.upper()}</span>
            </div>
        ''')
    return html_parts

def generate_alerts(health_data):
    """Generate alerts HTML"""
    alerts = []
    
    # Check for degraded services
    degraded_services = health_data.get('degraded_services', [])
    if degraded_services:
        alerts.append(f'''
            <div class="alert alert-warning">
                <strong>Degraded Services:</strong> {", ".join(degraded_services)}
            </div>
        ''')
    
    # Check for unhealthy services
    unhealthy_services = [k for k, v in health_data['services'].items() 
                         if v in ['unhealthy', 'unreachable']]
    if unhealthy_services:
        alerts.append(f'''
            <div class="alert alert-danger">
                <strong>Unhealthy Services:</strong> {", ".join(unhealthy_services)}
            </div>
        ''')
    
    if not alerts:
        return '<div class="alert alert-success">✅ All systems operational</div>'
    
    return ''.join(alerts)