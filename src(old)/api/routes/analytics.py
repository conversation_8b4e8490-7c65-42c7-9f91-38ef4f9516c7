"""
Analytics API routes for market data analysis and quality assessment.
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import pandas as pd

# Updated imports after massive refactoring
from src.shared.data_providers.unified_base import QualityScore as BaseQualityScore
from src.bot.pipeline.core.context_manager import QualityScore, DataQuality
from src.shared.data_validation import assess_data_quality
from src.api.data.providers.data_source_manager import DataSourceManager
from src.api.data.cache import MarketDataCache
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

# Wrapper function to bridge old and new data quality APIs
async def score_data_quality(symbol: str, data: pd.DataFrame, interval_type: str = '1d', provider_metadata: dict = None) -> QualityScore:
    """
    Bridge function to maintain compatibility with old score_data_quality API.
    Uses the new assess_data_quality function and converts to QualityScore format.
    """
    try:
        # Use the new data validation function
        quality_assessment = assess_data_quality(data, symbol, interval_type)

        # Convert to QualityScore format
        overall_score = quality_assessment.get('quality_score', 0.0)
        completeness = quality_assessment.get('completeness', 0.0)

        # Calculate other scores based on available data
        freshness_score = 85.0 if not data.empty else 0.0  # Assume fresh if we have data
        consistency_score = max(0.0, overall_score - 10)  # Slightly lower than overall
        source_reliability = 80.0 if provider_metadata and not provider_metadata.get('is_fallback', False) else 60.0

        return QualityScore(
            overall_score=overall_score,
            freshness_score=freshness_score,
            consistency_score=consistency_score,
            completeness_score=completeness,
            source_reliability=source_reliability
        )
    except Exception as e:
        logger.error(f"Error scoring data quality for {symbol}: {e}")
        return QualityScore()  # Return default (all zeros)

def get_quality_level_description(quality_level: DataQuality) -> str:
    """
    Get human-readable description for data quality levels.
    Bridge function to replace missing get_quality_level_description.
    """
    descriptions = {
        DataQuality.EXCELLENT: "Excellent data quality - highly reliable for trading decisions",
        DataQuality.GOOD: "Good data quality - suitable for most trading decisions with minor caution",
        DataQuality.FAIR: "Fair data quality - usable but requires additional verification",
        DataQuality.POOR: "Poor data quality - use with significant caution and additional sources",
        DataQuality.UNRELIABLE: "Unreliable data quality - not recommended for trading decisions"
    }
    return descriptions.get(quality_level, "Unknown quality level")

router = APIRouter(prefix="/analytics", tags=["analytics"])


@router.get("/quality/{symbol}")
async def get_data_quality_score(
    symbol: str,
    interval_type: str = Query("1d", description="Data interval type (1m, 5m, 15m, 1h, 1d)"),
    days: int = Query(30, description="Number of days of historical data to analyze"),
    include_gaps: bool = Query(True, description="Include detailed gap information"),
    include_recommendations: bool = Query(True, description="Include quality improvement recommendations")
) -> Dict[str, Any]:
    """
    Get comprehensive data quality score for a symbol.
    
    Returns detailed quality assessment including:
    - Overall quality score (0-100)
    - Breakdown by category (completeness, freshness, consistency, provider reliability)
    - Gap analysis and penalties
    - Quality level classification
    - Recommendations for improvement
    """
    try:
        logger.info(f"Quality assessment requested for {symbol} ({interval_type}, {days} days)")
        
        # Fetch historical data
        data_manager = DataSourceManager()
        historical_data = await data_manager.fetch_historical_data(symbol, days=days)
        
        if not historical_data:
            raise HTTPException(
                status_code=404, 
                detail=f"No historical data available for {symbol}"
            )
        
        # Convert to DataFrame if needed
        import pandas as pd
        if isinstance(historical_data, list):
            # Convert list of MarketDataResponse to DataFrame
            data_dicts = []
            for item in historical_data:
                if hasattr(item, 'to_dict'):
                    data_dict = item.to_dict()
                    # Extract timestamp and price for DataFrame
                    data_dicts.append({
                        'timestamp': data_dict.get('timestamp'),
                        'open': data_dict.get('open', data_dict.get('price')),
                        'high': data_dict.get('high', data_dict.get('price')),
                        'low': data_dict.get('low', data_dict.get('price')),
                        'close': data_dict.get('close', data_dict.get('price')),
                        'volume': data_dict.get('volume', 0)
                    })
            
            if data_dicts:
                df = pd.DataFrame(data_dicts)
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df = df.set_index('timestamp').sort_index()
            else:
                df = pd.DataFrame()
        else:
            df = historical_data
        
        # Get provider metadata from first data point
        provider_metadata = None
        if historical_data and hasattr(historical_data[0], 'metadata'):
            provider_metadata = historical_data[0].metadata.to_dict() if historical_data[0].metadata else None
        
        # Score data quality
        quality_score = await score_data_quality(
            symbol=symbol,
            data=df,
            interval_type=interval_type,
            provider_metadata=provider_metadata
        )
        
        # Build response
        response = {
            'symbol': symbol,
            'interval_type': interval_type,
            'analysis_period_days': days,
            'quality_score': quality_score.to_dict(),
            'quality_level_description': get_quality_level_description(quality_score.quality_level),
            'timestamp': datetime.now().isoformat()
        }
        
        # Add gap information if requested
        if include_gaps and hasattr(quality_score, 'gaps'):
            from src.shared.data_validation import detect_data_gaps
            gaps = detect_data_gaps(df, symbol, interval_type)
            response['gaps'] = [gap.to_dict() for gap in gaps] if gaps else []
            response['gap_count'] = len(response['gaps'])
        
        # Add recommendations if requested
        if include_recommendations:
            response['recommendations'] = _generate_quality_recommendations(quality_score)
        
        # Add data summary
        response['data_summary'] = {
            'total_data_points': len(df) if not df.empty else 0,
            'data_window_start': quality_score.data_window_start.isoformat() if quality_score.data_window_start else None,
            'data_window_end': quality_score.data_window_end.isoformat() if quality_score.data_window_end else None,
            'data_coverage_hours': _calculate_data_coverage_hours(quality_score.data_window_start, quality_score.data_window_end)
        }
        
        logger.info(f"Quality assessment completed for {symbol}: {quality_score.overall_score:.1f}/100")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error assessing data quality for {symbol}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal error during quality assessment: {str(e)}"
        )


@router.get("/quality/batch")
async def get_batch_data_quality(
    symbols: str = Query(..., description="Comma-separated list of symbols"),
    interval_type: str = Query("1d", description="Data interval type"),
    days: int = Query(30, description="Number of days of historical data")
) -> Dict[str, Any]:
    """
    Get data quality scores for multiple symbols in batch.
    
    Useful for monitoring data quality across multiple symbols efficiently.
    """
    try:
        symbol_list = [s.strip().upper() for s in symbols.split(",")]
        if len(symbol_list) > 50:  # Limit batch size
            raise HTTPException(
                status_code=400,
                detail="Batch size limited to 50 symbols"
            )
        
        logger.info(f"Batch quality assessment requested for {len(symbol_list)} symbols")
        
        results = {}
        data_manager = DataSourceManager()
        
        for symbol in symbol_list:
            try:
                # Fetch data for each symbol
                historical_data = await data_manager.fetch_historical_data(symbol, days=days)
                
                if not historical_data:
                    results[symbol] = {
                        'error': 'No data available',
                        'quality_score': 0.0
                    }
                    continue
                
                # Convert to DataFrame
                import pandas as pd
                data_dicts = []
                for item in historical_data:
                    if hasattr(item, 'to_dict'):
                        data_dict = item.to_dict()
                        data_dicts.append({
                            'timestamp': data_dict.get('timestamp'),
                            'open': data_dict.get('open', data_dict.get('price')),
                            'high': data_dict.get('high', data_dict.get('price')),
                            'low': data_dict.get('low', data_dict.get('price')),
                            'close': data_dict.get('close', data_dict.get('price')),
                            'volume': data_dict.get('volume', 0)
                        })
                
                if data_dicts:
                    df = pd.DataFrame(data_dicts)
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    df = df.set_index('timestamp').sort_index()
                    
                    # Get provider metadata
                    provider_metadata = None
                    if historical_data and hasattr(historical_data[0], 'metadata'):
                        provider_metadata = historical_data[0].metadata.to_dict() if historical_data[0].metadata else None
                    
                    # Score quality
                    quality_score = await score_data_quality(
                        symbol=symbol,
                        data=df,
                        interval_type=interval_type,
                        provider_metadata=provider_metadata
                    )
                    
                    results[symbol] = {
                        'quality_score': quality_score.overall_score,
                        'quality_level': quality_score.quality_level.value,
                        'completeness': quality_score.completeness_score,
                        'freshness': quality_score.freshness_score,
                        'gap_count': len(quality_score.gaps) if hasattr(quality_score, 'gaps') else 0
                    }
                else:
                    results[symbol] = {
                        'error': 'No valid data points',
                        'quality_score': 0.0
                    }
                    
            except Exception as e:
                logger.warning(f"Error assessing quality for {symbol}: {e}")
                results[symbol] = {
                    'error': str(e),
                    'quality_score': 0.0
                }
        
        # Calculate batch summary
        successful_assessments = [r for r in results.values() if 'error' not in r]
        if successful_assessments:
            avg_quality = sum(r['quality_score'] for r in successful_assessments) / len(successful_assessments)
            quality_distribution = {}
            for result in successful_assessments:
                level = result['quality_level']
                quality_distribution[level] = quality_distribution.get(level, 0) + 1
        else:
            avg_quality = 0.0
            quality_distribution = {}
        
        return {
            'batch_summary': {
                'total_symbols': len(symbol_list),
                'successful_assessments': len(successful_assessments),
                'failed_assessments': len(symbol_list) - len(successful_assessments),
                'average_quality_score': round(avg_quality, 2),
                'quality_distribution': quality_distribution
            },
            'results': results,
            'timestamp': datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in batch quality assessment: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal error during batch assessment: {str(e)}"
        )


def _generate_quality_recommendations(quality_score: QualityScore) -> List[str]:
    """Generate recommendations for improving data quality."""
    recommendations = []
    
    # Overall quality recommendations
    if quality_score.overall_score < 50:
        recommendations.append("Data quality is critically low - consider using alternative data sources")
    elif quality_score.overall_score < 70:
        recommendations.append("Data quality is below optimal - verify analysis results independently")
    
    # Specific area recommendations
    if quality_score.completeness_score < 80:
        recommendations.append("Data completeness is low - consider extending data collection period")
    
    if quality_score.freshness_score < 70:
        recommendations.append("Data freshness is poor - consider more frequent updates")
    
    if quality_score.consistency_score < 80:
        recommendations.append("Data consistency issues detected - validate data integrity")
    
    if quality_score.provider_reliability_score < 70:
        recommendations.append("Provider reliability is low - consider switching data sources")
    
    if quality_score.gap_penalty > 20:
        recommendations.append("Significant data gaps detected - implement gap-filling strategies")
    
    # Positive feedback
    if quality_score.overall_score >= 90:
        recommendations.append("Data quality is excellent - no immediate improvements needed")
    elif quality_score.overall_score >= 80:
        recommendations.append("Data quality is good - minor optimizations may be beneficial")
    
    return recommendations


def _calculate_data_coverage_hours(start_time: Optional[datetime], end_time: Optional[datetime]) -> Optional[float]:
    """Calculate data coverage in hours."""
    if not start_time or not end_time:
        return None
    
    try:
        from datetime import timezone
        if start_time.tzinfo is None:
            start_time = start_time.replace(tzinfo=timezone.utc)
        if end_time.tzinfo is None:
            end_time = end_time.replace(tzinfo=timezone.utc)
        
        duration = end_time - start_time
        return duration.total_seconds() / 3600  # Convert to hours
        
    except Exception:
        return None