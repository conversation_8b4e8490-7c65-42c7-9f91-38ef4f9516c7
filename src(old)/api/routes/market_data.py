from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import JSONResponse
from fastapi.security import OAuth2PasswordBearer
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import asyncio
import logging
import re

from pydantic import BaseModel, Field, validator

from src.api.middleware.security import generate_temporary_user_token, decode_token
from src.api.data.market_data_service import market_data_service
from src.api.data.cache import market_data_cache
from src.api.data.providers.base import MarketDataResponse
from src.core.config_manager import get_config
from src.shared.error_handling.logging import get_logger
from src.database.unified_db import get_supabase_client

logger = get_logger(__name__)

market_data_router = APIRouter(prefix="/market", tags=["Market Data"])

# OAuth2 scheme for authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/token")

class MarketDataRequest(BaseModel):
    """
    Market data request model with enhanced validation.
    """
    symbol: str = Field(..., description="Stock symbol", min_length=1, max_length=10)
    start_date: Optional[datetime] = Field(
        None, 
        description="Start date for historical data"
    )
    end_date: Optional[datetime] = Field(
        None, 
        description="End date for historical data"
    )
    days: int = Field(
        30, 
        description="Number of days of historical data to retrieve", 
        ge=1, 
        le=365
    )
    force_refresh: bool = Field(
        False,
        description="Force refresh from data provider, bypassing cache"
    )

    @validator('symbol')
    def validate_symbol(cls, v):
        """Validate stock symbol format."""
        if not re.match(r'^[A-Z]{1,5}$', v):
            raise ValueError('Symbol must be 1-5 uppercase letters only')
        return v

    @validator('end_date')
    def validate_date_range(cls, v, values):
        """Validate date range logic."""
        if v and 'start_date' in values and values['start_date']:
            if v <= values['start_date']:
                raise ValueError('End date must be after start date')
        return v

class MarketDataAnalysis(BaseModel):
    """
    Enhanced market data analysis response.
    """
    symbol: str
    current_price: float
    historical_data: List[MarketDataResponse]
    price_change_percent: float
    volatility: float
    trend: str
    cached: bool = False

async def get_current_user(token: str = Depends(oauth2_scheme)):
    """Get current authenticated user."""
    try:
        payload = decode_token(token)
        return payload
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

@market_data_router.post("/current", response_model=MarketDataResponse)
async def get_current_price(
    request: MarketDataRequest,
    current_user: Dict = Depends(get_current_user)
):
    """
    Get current price for a stock symbol.
    
    Args:
        request (MarketDataRequest): Market data request details
        current_user: Authenticated user (dependency)
    
    Returns:
        MarketDataResponse: Current market data
    """
    try:
        # Log request for audit trail
        logger.info(f"User {current_user.get('sub', 'unknown')} requested price for {request.symbol}")
        
        # If force_refresh is True, bypass cache
        if request.force_refresh:
            return await market_data_service.get_current_price(request.symbol)
        
        # Try to get cached data first
        cached_price = await market_data_cache.get_current_price(request.symbol)
        if cached_price:
            return cached_price
        
        # If no cache, fetch from provider and cache
        price_data = await market_data_service.get_current_price(request.symbol)
        await market_data_cache.set_current_price(request.symbol, price_data)
        
        return price_data
    
    except Exception as e:
        logger.error(f"Error retrieving current price for {request.symbol}: {e}")
        raise HTTPException(
            status_code=404, 
            detail=f"Unable to retrieve price for {request.symbol}"
        )

@market_data_router.post("/historical", response_model=List[MarketDataResponse])
async def get_historical_data(
    request: MarketDataRequest,
    current_user: Dict = Depends(get_current_user)
):
    """
    Get historical market data for a stock symbol.
    
    Args:
        request (MarketDataRequest): Market data request details
        current_user: Authenticated user (dependency)
    
    Returns:
        List[MarketDataResponse]: Historical market data
    """
    try:
        # Log request for audit trail
        logger.info(f"User {current_user.get('sub', 'unknown')} requested historical data for {request.symbol}")
        
        # Validate date range
        if request.start_date and request.end_date:
            if request.end_date <= request.start_date:
                raise HTTPException(
                    status_code=400,
                    detail="End date must be after start date"
                )
        
        # Get historical data
        historical_data = await market_data_service.get_historical_data(
            request.symbol,
            request.start_date,
            request.end_date,
            request.days
        )
        
        return historical_data
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving historical data for {request.symbol}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Unable to retrieve historical data for {request.symbol}"
        )

@market_data_router.post("/analysis", response_model=MarketDataAnalysis)
async def get_market_data_analysis(
    request: MarketDataRequest,
):
    """
    Perform comprehensive market data analysis.
    
    Args:
        request (MarketDataRequest): Market data request details
        _user: Authenticated user (dependency)
    
    Returns:
        MarketDataAnalysis: Detailed market data analysis
    """
    try:
        # If force_refresh is True, bypass cache
        if request.force_refresh:
            current_data = await market_data_service.get_current_price(request.symbol)
            historical_data = await market_data_service.get_historical_data(
                request.symbol, 
                request.start_date, 
                request.end_date, 
                request.days
            )
            cached = False
        else:
            # Try to get cached data first
            current_data = await market_data_cache.get_current_price(request.symbol)
            historical_data = await market_data_cache.get_historical_data(
                request.symbol, 
                request.start_date, 
                request.end_date
            )
            
            # If no cache, fetch from provider and cache
            if not current_data:
                current_data = await market_data_service.get_current_price(request.symbol)
                await market_data_cache.set_current_price(request.symbol, current_data)
            
            if not historical_data:
                historical_data = await market_data_service.get_historical_data(
                    request.symbol, 
                    request.start_date, 
                    request.end_date, 
                    request.days
                )
                await market_data_cache.set_historical_data(
                    request.symbol, 
                    historical_data, 
                    request.start_date, 
                    request.end_date
                )
            
            cached = True
        
        # Calculate price change percentage
        if len(historical_data) > 1:
            first_price = historical_data[0].price
            last_price = historical_data[-1].price
            price_change_percent = ((last_price - first_price) / first_price) * 100
        else:
            price_change_percent = 0.0
        
        # Calculate volatility (standard deviation of prices)
        prices = [data.price for data in historical_data]
        volatility = (
            (sum((p - sum(prices)/len(prices))**2 for p in prices) / len(prices))**0.5 
            if prices else 0.0
        )
        
        # Determine trend
        trend = (
            "Bullish" if price_change_percent > 0 else 
            "Bearish" if price_change_percent < 0 else 
            "Neutral"
        )
        
        return MarketDataAnalysis(
            symbol=request.symbol,
            current_price=current_data.price,
            historical_data=historical_data,
            price_change_percent=price_change_percent,
            volatility=volatility,
            trend=trend,
            cached=cached
        )
    
    except Exception as e:
        logger.error(f"Error performing market data analysis for {request.symbol}: {e}")
        raise HTTPException(
            status_code=404, 
            detail=f"Unable to perform analysis for {request.symbol}"
        )

@market_data_router.get("/symbols", response_model=List[str])
async def get_supported_symbols(
    query: Optional[str] = Query(None, description="Filter symbols"),
    limit: int = Query(10, ge=1, le=100, description="Maximum number of symbols"),
):
    """
    Get list of supported stock symbols.
    
    Args:
        query (str, optional): Filter symbols by prefix
        limit (int, optional): Maximum number of symbols to return
        _user: Authenticated user (dependency)
    
    Returns:
        List[str]: List of supported stock symbols
    """
    # In a real-world scenario, this would come from a database or external service
    all_symbols = [
        "AAPL", "GOOGL", "MSFT", "AMZN", "TSLA", "NVDA", 
        "META", "NFLX", "INTC", "AMD", "PYPL", "ADBE"
    ]
    
    # Filter symbols
    if query:
        all_symbols = [
            symbol for symbol in all_symbols 
            if symbol.lower().startswith(query.lower())
        ]
    
    return all_symbols[:limit] 