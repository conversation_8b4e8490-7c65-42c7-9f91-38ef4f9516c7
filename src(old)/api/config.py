"""
API Configuration Module

Provides API-specific configuration by importing from the centralized config manager.
All environment variable access is centralized in src.core.config_manager.
"""

from typing import Dict, Any
from src.core.config_manager import get_config


def get_api_config() -> Dict[str, Any]:
    """
    Get API configuration from centralized config manager
    
    Returns:
        Dictionary containing API configuration
    """
    config = get_config()
    return config.get_section('api')


def get_api_host() -> str:
    """Get API host from centralized config"""
    config = get_config()
    return config.get('api', 'host', '0.0.0.0')


def get_api_port() -> int:
    """Get API port from centralized config"""
    config = get_config()
    return config.get('api', 'port', 8000)


def get_cors_origins() -> list:
    """Get CORS origins from centralized config"""
    config = get_config()
    return config.get('api', 'cors_origins', [])


def get_rate_limit_config() -> Dict[str, int]:
    """Get rate limiting configuration"""
    config = get_config()
    return {
        'requests': config.get('api', 'rate_limit_requests', 100),
        'window': config.get('api', 'rate_limit_window', 60)
    }


def get_config_summary() -> Dict[str, Any]:
    """Get API configuration summary"""
    config = get_config()
    api_config = config.get_section('api')
    
    return {
        "host": api_config.get('host'),
        "port": api_config.get('port'),
        "cors_origins": api_config.get('cors_origins'),
        "rate_limiting": {
            "requests": api_config.get('rate_limit_requests'),
            "window": api_config.get('rate_limit_window')
        },
        "request_timeout": api_config.get('request_timeout')
    }