"""
Market Data API endpoints for fetching stock data.
"""
from fastapi import APIRouter, HTTPException, Query
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
import pandas as pd
import json

# Import the data provider manager
from src.shared.data_providers.aggregator import DataProviderAggregator
from src.shared.data_providers.unified_base import MarketDataRequest

# Initialize aggregator as data_provider_manager for backward compatibility
data_provider_manager = DataProviderAggregator()

router = APIRouter(
    prefix="/api/market",
    tags=["market"],
    responses={404: {"description": "Not found"}},
)

# Initialize the data provider manager
data_provider_manager.initialize_providers()

@router.get("/price/{symbol}")
async def get_current_price(symbol: str):
    """
    Get the current market price for a given symbol.
    
    Args:
        symbol: Stock symbol (e.g., AAPL, MSFT)
        
    Returns:
        dict: Current price information
    """
    try:
        # Create a request for price data
        request = MarketDataRequest(
            symbol=symbol,
            data_types=["price"]
        )
        
        # Get data from provider manager with automatic failover
        response = await data_provider_manager.get_market_data(symbol, ["price"])
        
        if not response or not response.price:
            raise HTTPException(status_code=404, detail=f"Could not fetch price for {symbol}")
            
        return {
            "symbol": symbol.upper(),
            "price": response.price,
            "timestamp": datetime.utcnow().isoformat(),
            "source": response.provider_name
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/historical/{symbol}")
async def get_historical(
    symbol: str,
    start_date: str = Query(..., description="Start date in YYYY-MM-DD format"),
    end_date: Optional[str] = Query(None, description="End date in YYYY-MM-DD format (defaults to today)"),
    interval: str = Query("1d", description="Data interval (1d, 1wk, 1mo)")
):
    """
    Get historical price data for a symbol.
    
    Args:
        symbol: Stock symbol
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format (optional)
        interval: Data interval (1d, 1wk, 1mo)
        
    Returns:
        dict: Historical price data
    """
    try:
        # Convert dates to datetime objects for validation
        try:
            start = datetime.strptime(start_date, "%Y-%m-%d")
            end = datetime.strptime(end_date, "%Y-%m-%d") if end_date else datetime.utcnow()
            
            if start > end:
                raise ValueError("Start date cannot be after end date")
                
            # Limit date range to prevent excessive data loading
            if (end - start).days > 365 * 5:  # 5 years max
                raise HTTPException(status_code=400, detail="Date range too large. Maximum 5 years of data allowed.")
                
        except ValueError as e:
            raise HTTPException(status_code=400, detail=f"Invalid date format: {str(e)}")
        
        # Create a request for historical data
        request = MarketDataRequest(
            symbol=symbol,
            data_types=["historical"],
            params={
                "start_date": start_date,
                "end_date": end_date if end_date else datetime.utcnow().strftime("%Y-%m-%d"),
                "interval": interval
            }
        )
        
        # Get data from provider manager with automatic failover
        response = await data_provider_manager.get_market_data(
            symbol, 
            ["historical"],
        )
        
        if not response or not hasattr(response, 'historical_data') or response.historical_data.empty:
            raise HTTPException(status_code=404, detail=f"No data found for {symbol}")
        
        # Get the historical data from the response
        data = response.historical_data
        
        # Convert DataFrame to JSON-serializable format
        # Reset index to include Date as a column
        data = data.reset_index()
        
        # Convert datetime to string
        if 'Date' in data.columns:
            data['Date'] = data['Date'].dt.strftime('%Y-%m-%d %H:%M:%S')
            
        return {
            "symbol": symbol.upper(),
            "data": data.to_dict(orient="records"),
            "start_date": start_date,
            "end_date": end_date if end_date else datetime.utcnow().strftime("%Y-%m-%d"),
            "interval": interval,
            "source": response.provider_name
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching historical data: {str(e)}")

@router.get("/intraday/{symbol}")
async def get_intraday(
    symbol: str,
    interval: str = Query("5m", description="Data interval (1m, 5m, 15m, 30m, 60m, 90m, 1h)"),
    days: int = Query(1, description="Number of days of data to fetch (max 7)")
):
    """
    Get intraday price data for a symbol.
    
    Args:
        symbol: Stock symbol
        interval: Data interval (1m, 5m, 15m, 30m, 60m, 90m, 1h)
        days: Number of days of data to fetch (max 7)
        
    Returns:
        dict: Intraday price data
    """
    try:
        # Validate days parameter
        if days < 1 or days > 7:
            raise HTTPException(status_code=400, detail="Days must be between 1 and 7")
            
        # Calculate start date
        end_date = datetime.utcnow()
        start_date = (end_date - timedelta(days=days)).strftime("%Y-%m-%d")
        
        # Create a request for intraday data
        request = MarketDataRequest(
            symbol=symbol,
            data_types=["intraday"],
            params={
                "interval": interval,
                "days": days
            }
        )
        
        # Get data from provider manager with automatic failover
        response = await data_provider_manager.get_market_data(
            symbol, 
            ["intraday"],
        )
        
        if not response or not hasattr(response, 'intraday_data') or response.intraday_data.empty:
            raise HTTPException(status_code=404, detail=f"No intraday data found for {symbol}")
        
        # Get the intraday data from the response
        data = response.intraday_data
        
        # Convert DataFrame to JSON-serializable format
        data = data.reset_index()
        
        # Convert datetime to string
        if 'Datetime' in data.columns:
            data['Datetime'] = data['Datetime'].dt.strftime('%Y-%m-%d %H:%M:%S')
        
        return {
            "symbol": symbol.upper(),
            "data": data.to_dict(orient="records"),
            "interval": interval,
            "days": days,
            "source": response.provider_name
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching intraday data: {str(e)}")
