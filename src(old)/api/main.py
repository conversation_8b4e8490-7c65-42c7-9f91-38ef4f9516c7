# Third-party imports
import async<PERSON>
import uvicorn
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Local imports
from src.core.config_manager import get_config
from src.shared.error_handling.logging import get_logger
from src.database.unified_db import initialize_database
from src.api.routes.health import router as health_router
# from src.api.routes.auth import auth_router  # Removed - not needed for Discord bot
from src.api.routes.market_data import market_data_router
from src.api.routes.analytics import router as analytics_router
from src.api.middleware.security import setup_security_middleware
from src.api.routes.metrics import metrics_router
from src.api.routes.feedback import feedback_router
from src.api.routes import debug as debug_router_module

# Configure logging
logger = get_logger(__name__)

def create_application() -> FastAPI:
    """
    Create and configure FastAPI application.
    
    Returns:
        FastAPI: Configured application instance
    """
    # Application settings
    config = get_config()
    is_prod = not config.get('app', 'debug', False)
    
    # Initialize database engine
    try:
        logger.info("Initializing database engine")
        # Defer database initialization to avoid event loop conflicts
        # The database will be initialized when first accessed
        logger.info("Database engine initialization deferred to first access")
    except Exception as e:
        logger.error(f"Failed to initialize database engine: {e}")
        # Continue without database - health checks will report the issue
    
    # Create FastAPI app
    app = FastAPI(
        title=config.get('app', 'name', 'AI Trading Bot Backend'),
        description="AI Trading Bot Backend",
        version=config.get('app', 'version', '1.0.0'),
        docs_url=None if is_prod else "/docs",
        redoc_url=None if is_prod else "/redoc",
        openapi_url=None if is_prod else "/openapi.json"
    )

    # Error handling middleware - logs requests and responses with proper status codes
    @app.middleware("http")
    async def error_handling_middleware(request: Request, call_next):
        try:
            # Process the request and get the response
            response = await call_next(request)
            
            # Log non-success responses
            if response.status_code >= 400:
                logger.warning(
                    f"Request to {request.url.path} returned {response.status_code}"
                )
                
            return response
        except Exception as e:
            # Log the exception
            logger.error(f"Error processing request to {request.url.path}: {e}", exc_info=True)
            
            # Return a proper error response with appropriate status code
            status_code = 500  # Internal Server Error by default
            
            # Customize status code based on exception type if needed
            if hasattr(e, 'status_code'):
                status_code = e.status_code
                
            return JSONResponse(
                status_code=status_code,
                content={
                    "message": "An unexpected error occurred",
                    "error": str(e),
                    "path": request.url.path
                }
            )
    
    # CORS Middleware - configuration handled by centralized config manager
    cors_origins = config.get('api', 'cors_origins', [])
    
    app.add_middleware(
        CORSMiddleware,
        allow_origins=cors_origins,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE"],
        allow_headers=["*"],
        expose_headers=["X-Total-Count"],
        max_age=600,  # Cache preflight for 10 minutes
    )
    
    # Security Middleware
    app = setup_security_middleware(app)
    
    # Include routers
    app.include_router(health_router, tags=["Health"])
    # app.include_router(auth_router, prefix="/auth", tags=["Authentication"])  # Removed
    app.include_router(market_data_router, prefix="/api", tags=["Market Data"])
    app.include_router(analytics_router, prefix="/api", tags=["Analytics"])
    
    # Include new routers
    app.include_router(metrics_router, prefix="/api/v1")
    app.include_router(feedback_router, prefix="/api/v1")
    # Debug console routes (SSE + posting debug events)
    app.include_router(debug_router_module.router, prefix="/api/debug")
    
    # Test endpoint at root level
    @app.get("/test")
    async def test_endpoint():
        """
        Simple test endpoint to verify API functionality
        
        Returns:
            dict: Test response
        """
        return {
            "message": "API test successful", 
            "timestamp": "2025-08-26",
            "status": "operational"
        }
    
    # Global exception handler
    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception):
        """
        Global exception handler for unhandled exceptions.
        """
        logger.error(f"Unhandled exception: {exc}", exc_info=True)
        
        # Determine appropriate status code
        status_code = 500  # Internal Server Error by default
        
        # Customize status code based on exception type
        if hasattr(exc, 'status_code'):
            status_code = exc.status_code
        
        return JSONResponse(
            status_code=status_code,
            content={
                "message": "An unexpected error occurred",
                "error": str(exc),
                "path": request.url.path
            }
        )
    
    return app

# Create the application instance
app = create_application()

if __name__ == "__main__":
    import os
    host = os.getenv('API_HOST', '0.0.0.0')
    port = int(os.getenv('API_PORT', '8000'))
    uvicorn.run(app, host=host, port=port)
 