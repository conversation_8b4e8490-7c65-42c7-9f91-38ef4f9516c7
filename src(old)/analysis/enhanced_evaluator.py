#!/usr/bin/env python3
"""
Enhanced Analysis Evaluator with Multi-Layered Validation
"""

import asyncio
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List, Tuple
from dataclasses import dataclass, asdict
from enum import Enum

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# Import existing components
try:
    from src.shared.technical_analysis.strategy_calculator import PriceTargetEngine, TrendDirection
    from src.analysis.templates.analysis_response_template import AnalysisResult
except ImportError:
    # If we can't import, create mock classes for demonstration
    class TrendDirection(Enum):
        BULLISH = "bullish"
        BEARISH = "bearish"
        SIDEWAYS = "sideways"
        UNKNOWN = "unknown"
    
    @dataclass
    class AnalysisResult:
        symbol: str
        current_price: float
        targets: Dict[str, float]
        technical_context: Dict[str, Any]
        support_levels: List[float]
        resistance_levels: List[float]

class ValidationLevel(Enum):
    """Validation confidence levels"""
    HIGH = "High"
    MEDIUM = "Medium"
    LOW = "Low"
    VERY_LOW = "Very Low"

@dataclass
class ValidationMetric:
    """Individual validation metric"""
    name: str
    score: float  # 0-100
    weight: float  # 0-1
    description: str
    validation_level: ValidationLevel

@dataclass
class ValidationResult:
    """Comprehensive validation result"""
    overall_confidence: float  # 0-100
    technical_validity: float  # 0-100
    market_context: float  # 0-100
    historical_reliability: float  # 0-100
    risk_assessment: float  # 0-100
    validation_metrics: List[ValidationMetric]
    validation_points: List[str]
    potential_issues: List[str]
    recommendations: List[str]
    self_explanation: str
    truth_verification: str

class EnhancedAnalysisEvaluator:
    """Enhanced evaluator with multi-layered validation"""
    
    def __init__(self):
        self.price_target_engine = PriceTargetEngine()
        
    def evaluate(self, analysis_result: AnalysisResult, 
                 market_context: Dict[str, Any] = None,
                 historical_data: pd.DataFrame = None) -> ValidationResult:
        """
        Perform comprehensive evaluation of analysis result
        
        Args:
            analysis_result: The analysis to evaluate
            market_context: Current market conditions
            historical_data: Historical price data
            
        Returns:
            Comprehensive validation result
        """
        # Initialize validation metrics
        validation_metrics = []
        validation_points = []
        potential_issues = []
        recommendations = []
        
        # Layer 1: Technical Validity Check
        technical_score, tech_metrics = self._evaluate_technical_validity(
            analysis_result, historical_data)
        validation_metrics.extend(tech_metrics)
        
        # Layer 2: Market Context Validation
        market_score, market_metrics = self._evaluate_market_context(
            analysis_result, market_context)
        validation_metrics.extend(market_metrics)
        
        # Layer 3: Historical Reliability Assessment
        historical_score, hist_metrics = self._evaluate_historical_reliability(
            analysis_result, historical_data)
        validation_metrics.extend(hist_metrics)
        
        # Layer 4: Risk Validation
        risk_score, risk_metrics = self._evaluate_risk_validation(
            analysis_result)
        validation_metrics.extend(risk_metrics)
        
        # Calculate weighted overall confidence
        overall_confidence = self._calculate_weighted_confidence(validation_metrics)
        
        # Generate self-explanation
        self_explanation = self._generate_self_explanation(
            validation_metrics, overall_confidence)
        
        # Perform truth verification
        truth_verification = self._perform_truth_verification(
            analysis_result, market_context)
        
        # Generate recommendations based on issues
        recommendations.extend(self._generate_recommendations(
            validation_metrics, overall_confidence))
        
        # Collect validation points and issues
        validation_points.extend(self._collect_validation_points(validation_metrics))
        potential_issues.extend(self._collect_potential_issues(validation_metrics))
        
        return ValidationResult(
            overall_confidence=overall_confidence,
            technical_validity=technical_score,
            market_context=market_score,
            historical_reliability=historical_score,
            risk_assessment=risk_score,
            validation_metrics=validation_metrics,
            validation_points=validation_points,
            potential_issues=potential_issues,
            recommendations=recommendations,
            self_explanation=self_explanation,
            truth_verification=truth_verification
        )
    
    def _evaluate_technical_validity(self, analysis_result: AnalysisResult, 
                                   historical_data: pd.DataFrame) -> Tuple[float, List[ValidationMetric]]:
        """Evaluate technical validity of the analysis"""
        metrics = []
        
        # Indicator convergence (25 pts)
        convergence_score = self._calculate_indicator_convergence(analysis_result)
        metrics.append(ValidationMetric(
            name="Indicator Convergence",
            score=convergence_score,
            weight=0.25,
            description="How well different technical indicators agree",
            validation_level=self._determine_validation_level(convergence_score)
        ))
        
        # Mathematical soundness (20 pts)
        math_score = self._calculate_mathematical_soundness(analysis_result)
        metrics.append(ValidationMetric(
            name="Mathematical Soundness",
            score=math_score,
            weight=0.20,
            description="Correctness of calculations and logic",
            validation_level=self._determine_validation_level(math_score)
        ))
        
        # Support/resistance alignment (20 pts)
        sr_score = self._calculate_support_resistance_alignment(analysis_result)
        metrics.append(ValidationMetric(
            name="Support/Resistance Alignment",
            score=sr_score,
            weight=0.20,
            description="Alignment of targets with key levels",
            validation_level=self._determine_validation_level(sr_score)
        ))
        
        # Fibonacci confluence (15 pts)
        fib_score = self._calculate_fibonacci_confluence(analysis_result)
        metrics.append(ValidationMetric(
            name="Fibonacci Confluence",
            score=fib_score,
            weight=0.15,
            description="Alignment with Fibonacci levels",
            validation_level=self._determine_validation_level(fib_score)
        ))
        
        # Volume confirmation (20 pts)
        volume_score = self._calculate_volume_confirmation(analysis_result, historical_data)
        metrics.append(ValidationMetric(
            name="Volume Confirmation",
            score=volume_score,
            weight=0.20,
            description="Volume support for technical signals",
            validation_level=self._determine_validation_level(volume_score)
        ))
        
        # Calculate weighted technical score
        technical_score = self._calculate_weighted_subset_score(metrics)
        
        return technical_score, metrics
    
    def _evaluate_market_context(self, analysis_result: AnalysisResult,
                               market_context: Dict[str, Any]) -> Tuple[float, List[ValidationMetric]]:
        """Evaluate market context validity"""
        metrics = []
        
        # Sector alignment (25 pts)
        sector_score = self._calculate_sector_alignment(analysis_result, market_context)
        metrics.append(ValidationMetric(
            name="Sector Alignment",
            score=sector_score,
            weight=0.25,
            description="Alignment with sector performance",
            validation_level=self._determine_validation_level(sector_score)
        ))
        
        # Market regime fit (25 pts)
        regime_score = self._calculate_market_regime_fit(analysis_result, market_context)
        metrics.append(ValidationMetric(
            name="Market Regime Fit",
            score=regime_score,
            weight=0.25,
            description="Suitability for current market conditions",
            validation_level=self._determine_validation_level(regime_score)
        ))
        
        # News/event correlation (25 pts)
        news_score = self._calculate_news_correlation(analysis_result, market_context)
        metrics.append(ValidationMetric(
            name="News/Event Correlation",
            score=news_score,
            weight=0.25,
            description="Consistency with recent news/events",
            validation_level=self._determine_validation_level(news_score)
        ))
        
        # Liquidity assessment (25 pts)
        liquidity_score = self._calculate_liquidity_assessment(analysis_result, market_context)
        metrics.append(ValidationMetric(
            name="Liquidity Assessment",
            score=liquidity_score,
            weight=0.25,
            description="Adequacy of market liquidity",
            validation_level=self._determine_validation_level(liquidity_score)
        ))
        
        # Calculate weighted market context score
        market_score = self._calculate_weighted_subset_score(metrics)
        
        return market_score, metrics
    
    def _evaluate_historical_reliability(self, analysis_result: AnalysisResult,
                                       historical_data: pd.DataFrame) -> Tuple[float, List[ValidationMetric]]:
        """Evaluate historical reliability"""
        metrics = []
        
        # Backtest performance (40 pts)
        backtest_score = self._calculate_backtest_performance(analysis_result, historical_data)
        metrics.append(ValidationMetric(
            name="Backtest Performance",
            score=backtest_score,
            weight=0.40,
            description="Historical performance of similar setups",
            validation_level=self._determine_validation_level(backtest_score)
        ))
        
        # Similar pattern success rate (30 pts)
        pattern_score = self._calculate_pattern_success_rate(analysis_result, historical_data)
        metrics.append(ValidationMetric(
            name="Pattern Success Rate",
            score=pattern_score,
            weight=0.30,
            description="Success rate of similar patterns",
            validation_level=self._determine_validation_level(pattern_score)
        ))
        
        # Time decay analysis (30 pts)
        time_decay_score = self._calculate_time_decay_analysis(analysis_result, historical_data)
        metrics.append(ValidationMetric(
            name="Time Decay Analysis",
            score=time_decay_score,
            weight=0.30,
            description="Effectiveness over time horizons",
            validation_level=self._determine_validation_level(time_decay_score)
        ))
        
        # Calculate weighted historical reliability score
        historical_score = self._calculate_weighted_subset_score(metrics)
        
        return historical_score, metrics
    
    def _evaluate_risk_validation(self, analysis_result: AnalysisResult) -> Tuple[float, List[ValidationMetric]]:
        """Evaluate risk factors"""
        metrics = []
        
        # Stress test results (30 pts)
        stress_score = self._calculate_stress_test_results(analysis_result)
        metrics.append(ValidationMetric(
            name="Stress Test Results",
            score=stress_score,
            weight=0.30,
            description="Performance under adverse conditions",
            validation_level=self._determine_validation_level(stress_score)
        ))
        
        # Position sizing appropriateness (25 pts)
        sizing_score = self._calculate_position_sizing_appropriateness(analysis_result)
        metrics.append(ValidationMetric(
            name="Position Sizing",
            score=sizing_score,
            weight=0.25,
            description="Appropriateness of position sizing",
            validation_level=self._determine_validation_level(sizing_score)
        ))
        
        # Drawdown potential (25 pts)
        drawdown_score = self._calculate_drawdown_potential(analysis_result)
        metrics.append(ValidationMetric(
            name="Drawdown Potential",
            score=drawdown_score,
            weight=0.25,
            description="Potential maximum drawdown",
            validation_level=self._determine_validation_level(drawdown_score)
        ))
        
        # Risk/reward optimization (20 pts)
        rr_score = self._calculate_risk_reward_optimization(analysis_result)
        metrics.append(ValidationMetric(
            name="Risk/Reward Ratio",
            score=rr_score,
            weight=0.20,
            description="Optimization of risk vs reward",
            validation_level=self._determine_validation_level(rr_score)
        ))
        
        # Calculate weighted risk assessment score
        risk_score = self._calculate_weighted_subset_score(metrics)
        
        return risk_score, metrics
    
    # Technical Validity Helper Methods
    def _calculate_indicator_convergence(self, analysis_result: AnalysisResult) -> float:
        """Calculate how well different indicators converge"""
        # In a real implementation, this would check multiple indicators
        # For now, we'll use a simplified approach based on trend strength
        trend_strength = analysis_result.technical_context.get('trend_strength', 0.5)
        return min(100, trend_strength * 100)
    
    def _calculate_mathematical_soundness(self, analysis_result: AnalysisResult) -> float:
        """Check mathematical correctness"""
        # Verify that targets are logically consistent
        current_price = analysis_result.current_price
        targets = analysis_result.targets
        
        # Check if targets are in logical order
        conservative = targets.get('conservative', current_price)
        moderate = targets.get('moderate', current_price)
        aggressive = targets.get('aggressive', current_price)
        
        # Simple check - in right direction and reasonable spacing
        if conservative > current_price and moderate > conservative and aggressive > moderate:
            return 90.0  # High score for logical consistency
        elif conservative < current_price and moderate < conservative and aggressive < moderate:
            return 90.0  # High score for logical consistency (short direction)
        else:
            return 50.0  # Medium score for questionable logic
    
    def _calculate_support_resistance_alignment(self, analysis_result: AnalysisResult) -> float:
        """Check alignment with support/resistance levels"""
        current_price = analysis_result.current_price
        targets = analysis_result.targets
        support_levels = analysis_result.support_levels
        resistance_levels = analysis_result.resistance_levels
        
        if not resistance_levels:
            return 50.0  # Medium score if no resistance levels
        
        # Check if targets are near resistance levels
        moderate_target = targets.get('moderate', current_price)
        nearest_resistance = min(resistance_levels, key=lambda x: abs(x - moderate_target))
        
        # Calculate distance as percentage
        distance_pct = abs(nearest_resistance - moderate_target) / moderate_target
        
        # Score based on proximity (closer is better)
        if distance_pct < 0.01:  # Within 1%
            return 95.0
        elif distance_pct < 0.02:  # Within 2%
            return 85.0
        elif distance_pct < 0.05:  # Within 5%
            return 70.0
        else:
            return 40.0
    
    def _calculate_fibonacci_confluence(self, analysis_result: AnalysisResult) -> float:
        """Check alignment with Fibonacci levels"""
        # In a real implementation, this would check against actual Fibonacci levels
        # For now, we'll return a moderate score
        return 65.0
    
    def _calculate_volume_confirmation(self, analysis_result: AnalysisResult, 
                                     historical_data: pd.DataFrame) -> float:
        """Check volume confirmation for signals"""
        # In a real implementation, this would analyze volume data
        # For now, we'll return a moderate score
        return 60.0
    
    # Market Context Helper Methods
    def _calculate_sector_alignment(self, analysis_result: AnalysisResult,
                                  market_context: Dict[str, Any]) -> float:
        """Check alignment with sector performance"""
        # In a real implementation, this would compare with sector data
        return 55.0
    
    def _calculate_market_regime_fit(self, analysis_result: AnalysisResult,
                                   market_context: Dict[str, Any]) -> float:
        """Check suitability for current market conditions"""
        # In a real implementation, this would analyze market regime
        return 60.0
    
    def _calculate_news_correlation(self, analysis_result: AnalysisResult,
                                  market_context: Dict[str, Any]) -> float:
        """Check consistency with recent news/events"""
        # In a real implementation, this would correlate with news sentiment
        return 50.0
    
    def _calculate_liquidity_assessment(self, analysis_result: AnalysisResult,
                                      market_context: Dict[str, Any]) -> float:
        """Assess market liquidity"""
        # In a real implementation, this would analyze liquidity metrics
        return 70.0
    
    # Historical Reliability Helper Methods
    def _calculate_backtest_performance(self, analysis_result: AnalysisResult,
                                      historical_data: pd.DataFrame) -> float:
        """Calculate historical performance of similar setups"""
        # In a real implementation, this would run backtests
        return 65.0
    
    def _calculate_pattern_success_rate(self, analysis_result: AnalysisResult,
                                      historical_data: pd.DataFrame) -> float:
        """Calculate success rate of similar patterns"""
        # In a real implementation, this would identify and analyze patterns
        return 60.0
    
    def _calculate_time_decay_analysis(self, analysis_result: AnalysisResult,
                                     historical_data: pd.DataFrame) -> float:
        """Analyze effectiveness over time horizons"""
        # In a real implementation, this would analyze time decay
        return 55.0
    
    # Risk Validation Helper Methods
    def _calculate_stress_test_results(self, analysis_result: AnalysisResult) -> float:
        """Calculate performance under adverse conditions"""
        # In a real implementation, this would run stress tests
        return 70.0
    
    def _calculate_position_sizing_appropriateness(self, analysis_result: AnalysisResult) -> float:
        """Check appropriateness of position sizing"""
        # In a real implementation, this would analyze position sizing
        return 65.0
    
    def _calculate_drawdown_potential(self, analysis_result: AnalysisResult) -> float:
        """Calculate potential maximum drawdown"""
        # In a real implementation, this would calculate drawdown potential
        return 60.0
    
    def _calculate_risk_reward_optimization(self, analysis_result: AnalysisResult) -> float:
        """Calculate risk vs reward optimization"""
        current_price = analysis_result.current_price
        targets = analysis_result.targets
        stop_loss = targets.get('stop_loss', current_price)
        
        # Calculate risk/reward ratio
        moderate_target = targets.get('moderate', current_price)
        risk = abs(current_price - stop_loss)
        reward = abs(moderate_target - current_price)
        
        if risk > 0:
            rr_ratio = reward / risk
            # Score based on risk/reward ratio (higher is better, but capped)
            if rr_ratio > 3.0:
                return 95.0
            elif rr_ratio > 2.0:
                return 85.0
            elif rr_ratio > 1.5:
                return 75.0
            elif rr_ratio > 1.0:
                return 65.0
            else:
                return 40.0
        else:
            return 50.0  # Neutral score if no risk defined
    
    # Utility Methods
    def _calculate_weighted_confidence(self, metrics: List[ValidationMetric]) -> float:
        """Calculate overall confidence as weighted average"""
        if not metrics:
            return 50.0
        
        total_weighted_score = sum(metric.score * metric.weight for metric in metrics)
        total_weight = sum(metric.weight for metric in metrics)
        
        if total_weight > 0:
            return min(100, max(0, total_weighted_score / total_weight))
        else:
            return 50.0
    
    def _calculate_weighted_subset_score(self, metrics: List[ValidationMetric]) -> float:
        """Calculate weighted score for a subset of metrics"""
        return self._calculate_weighted_confidence(metrics)
    
    def _determine_validation_level(self, score: float) -> ValidationLevel:
        """Determine validation level based on score"""
        if score >= 80:
            return ValidationLevel.HIGH
        elif score >= 60:
            return ValidationLevel.MEDIUM
        elif score >= 40:
            return ValidationLevel.LOW
        else:
            return ValidationLevel.VERY_LOW
    
    def _generate_self_explanation(self, metrics: List[ValidationMetric], 
                                 overall_confidence: float) -> str:
        """Generate explanation of the evaluation process"""
        explanation = []
        explanation.append("This analysis was evaluated using a multi-layered validation approach:")
        explanation.append("")
        
        # Technical validity
        tech_metrics = [m for m in metrics if m.weight > 0 and 'Technical' in m.name or 
                       'Indicator' in m.name or 'Fibonacci' in m.name or 
                       'Support' in m.name or 'Volume' in m.name or 'Mathematical' in m.name]
        if tech_metrics:
            avg_tech = sum(m.score * m.weight for m in tech_metrics) / sum(m.weight for m in tech_metrics)
            explanation.append(f"• Technical Validity: {avg_tech:.1f}/100 - Based on indicator convergence,")
            explanation.append("  mathematical soundness, and alignment with key levels.")
        
        # Market context
        market_metrics = [m for m in metrics if 'Sector' in m.name or 'Market' in m.name or 
                         'News' in m.name or 'Liquidity' in m.name]
        if market_metrics:
            avg_market = sum(m.score * m.weight for m in market_metrics) / sum(m.weight for m in market_metrics)
            explanation.append(f"• Market Context: {avg_market:.1f}/100 - Evaluated against sector performance,")
            explanation.append("  market conditions, and liquidity factors.")
        
        # Historical reliability
        hist_metrics = [m for m in metrics if 'Backtest' in m.name or 'Pattern' in m.name or 
                       'Time Decay' in m.name]
        if hist_metrics:
            avg_hist = sum(m.score * m.weight for m in hist_metrics) / sum(m.weight for m in hist_metrics)
            explanation.append(f"• Historical Reliability: {avg_hist:.1f}/100 - Based on backtesting results,")
            explanation.append("  pattern success rates, and time decay analysis.")
        
        # Risk assessment
        risk_metrics = [m for m in metrics if 'Stress' in m.name or 'Position' in m.name or 
                       'Drawdown' in m.name or 'Risk/Reward' in m.name]
        if risk_metrics:
            avg_risk = sum(m.score * m.weight for m in risk_metrics) / sum(m.weight for m in risk_metrics)
            explanation.append(f"• Risk Assessment: {avg_risk:.1f}/100 - Analyzed stress test results,")
            explanation.append("  position sizing appropriateness, and risk/reward optimization.")
        
        explanation.append("")
        explanation.append(f"Overall Confidence: {overall_confidence:.1f}/100")
        explanation.append("")
        explanation.append("The system evaluates its own results by:")
        explanation.append("1. Checking technical indicator convergence")
        explanation.append("2. Validating mathematical correctness")
        explanation.append("3. Assessing alignment with market context")
        explanation.append("4. Analyzing historical reliability")
        explanation.append("5. Evaluating risk factors")
        
        return "\n".join(explanation)
    
    def _perform_truth_verification(self, analysis_result: AnalysisResult,
                                  market_context: Dict[str, Any]) -> str:
        """Perform truth verification of results"""
        verification = []
        verification.append("Truth Verification Results:")
        verification.append("")
        
        # Check basic logical consistency
        current_price = analysis_result.current_price
        targets = analysis_result.targets
        
        conservative = targets.get('conservative', current_price)
        moderate = targets.get('moderate', current_price)
        aggressive = targets.get('aggressive', current_price)
        stop_loss = targets.get('stop_loss', current_price)
        
        verification.append("Logical Consistency Check:")
        if (conservative > current_price and moderate > conservative and aggressive > moderate) or \
           (conservative < current_price and moderate < conservative and aggressive < moderate):
            verification.append("✓ Targets are logically ordered")
        else:
            verification.append("⚠ Warning: Targets may not be logically ordered")
        
        # Check risk/reward ratio
        risk = abs(current_price - stop_loss)
        reward = abs(moderate - current_price)
        
        if risk > 0:
            rr_ratio = reward / risk
            verification.append(f"✓ Risk/Reward Ratio: {rr_ratio:.2f}:1")
            if rr_ratio < 1.0:
                verification.append("⚠ Warning: Risk/reward ratio is less than 1:1")
        else:
            verification.append("⚠ Warning: No stop loss defined")
        
        # Check volatility appropriateness
        volatility = analysis_result.technical_context.get('volatility', 0.02)
        target_change = abs(moderate - current_price) / current_price
        
        verification.append(f"✓ Volatility Context: {volatility:.2%} daily")
        verification.append(f"✓ Target Change: {target_change:.2%}")
        
        if target_change > volatility * 10:  # More than 10 days of volatility
            verification.append("⚠ Warning: Target change seems large relative to volatility")
        
        verification.append("")
        verification.append("The analysis has been cross-checked for:")
        verification.append("• Logical consistency of price targets")
        verification.append("• Appropriate risk/reward ratios")
        verification.append("• Reasonable target sizes relative to volatility")
        verification.append("• Basic mathematical correctness")
        
        return "\n".join(verification)
    
    def _generate_recommendations(self, metrics: List[ValidationMetric], 
                                overall_confidence: float) -> List[str]:
        """Generate recommendations based on evaluation"""
        recommendations = []
        
        # Overall confidence based recommendations
        if overall_confidence < 40:
            recommendations.append("Consider seeking additional confirmation before acting on this analysis")
            recommendations.append("Use tighter position sizing due to low confidence")
        elif overall_confidence < 60:
            recommendations.append("Monitor the setup closely for confirmation signals")
            recommendations.append("Consider waiting for better market conditions")
        
        # Specific metric based recommendations
        low_confidence_metrics = [m for m in metrics if m.validation_level in [ValidationLevel.LOW, ValidationLevel.VERY_LOW]]
        
        for metric in low_confidence_metrics:
            if 'Indicator Convergence' in metric.name:
                recommendations.append("Seek additional technical confirmation from multiple indicators")
            elif 'Support/Resistance' in metric.name:
                recommendations.append("Wait for clearer support/resistance confirmation")
            elif 'Risk/Reward' in metric.name:
                recommendations.append("Review stop loss placement for better risk management")
            elif 'Volume' in metric.name:
                recommendations.append("Monitor volume for confirmation of breakout/breakdown")
            elif 'Market Regime' in metric.name:
                recommendations.append("Consider if current market conditions favor this setup")
            elif 'Liquidity' in metric.name:
                recommendations.append("Assess if there's adequate liquidity for this trade")
        
        # Deduplicate recommendations
        return list(set(recommendations))
    
    def _collect_validation_points(self, metrics: List[ValidationMetric]) -> List[str]:
        """Collect positive validation points"""
        points = []
        
        high_confidence_metrics = [m for m in metrics if m.validation_level == ValidationLevel.HIGH]
        
        for metric in high_confidence_metrics:
            points.append(f"High confidence in {metric.name} ({metric.score:.1f}/100)")
        
        medium_confidence_metrics = [m for m in metrics if m.validation_level == ValidationLevel.MEDIUM]
        
        for metric in medium_confidence_metrics:
            points.append(f"Medium confidence in {metric.name} ({metric.score:.1f}/100)")
        
        return points
    
    def _collect_potential_issues(self, metrics: List[ValidationMetric]) -> List[str]:
        """Collect potential issues"""
        issues = []
        
        low_confidence_metrics = [m for m in metrics if m.validation_level in [ValidationLevel.LOW, ValidationLevel.VERY_LOW]]
        
        for metric in low_confidence_metrics:
            issues.append(f"Low confidence in {metric.name} ({metric.score:.1f}/100)")
        
        return issues

# Enhanced Response Template
class EnhancedAnalysisResponseTemplate:
    """Enhanced response template with comprehensive evaluation"""
    
    def format_response(self, analysis_result: AnalysisResult, 
                       validation_result: ValidationResult) -> str:
        """Format comprehensive response with evaluation"""
        lines = []
        
        # Header
        lines.append("🤖 COMPREHENSIVE ANALYSIS WITH SELF-EVALUATION")
        lines.append("=" * 60)
        
        # Price targets
        lines.append(f"\n🎯 {analysis_result.symbol} PRICE TARGETS:")
        lines.append(f"  Current Price: ${analysis_result.current_price:.2f}")
        
        for target_name, target_price in analysis_result.targets.items():
            pct_change = ((target_price / analysis_result.current_price) - 1) * 100
            lines.append(f"  {target_name.capitalize()}: ${target_price:.2f} ({pct_change:+.1f}%)")
        
        # Overall evaluation
        lines.append(f"\n📊 OVERALL EVALUATION:")
        lines.append(f"  Overall Confidence: {validation_result.overall_confidence:.1f}/100")
        
        # Component scores
        lines.append(f"  Technical Validity: {validation_result.technical_validity:.1f}/100")
        lines.append(f"  Market Context: {validation_result.market_context:.1f}/100")
        lines.append(f"  Historical Reliability: {validation_result.historical_reliability:.1f}/100")
        lines.append(f"  Risk Assessment: {validation_result.risk_assessment:.1f}/100")
        
        # Validation points
        if validation_result.validation_points:
            lines.append(f"\n✅ VALIDATION POINTS:")
            for point in validation_result.validation_points[:5]:  # Limit to top 5
                lines.append(f"  • {point}")
        
        # Potential issues
        if validation_result.potential_issues:
            lines.append(f"\n⚠️  POTENTIAL ISSUES:")
            for issue in validation_result.potential_issues[:5]:  # Limit to top 5
                lines.append(f"  • {issue}")
        
        # Recommendations
        if validation_result.recommendations:
            lines.append(f"\n💡 RECOMMENDATIONS:")
            for rec in validation_result.recommendations:
                lines.append(f"  • {rec}")
        
        # Detailed metrics
        lines.append(f"\n🔍 DETAILED VALIDATION METRICS:")
        for metric in validation_result.validation_metrics:
            level_indicator = "✓" if metric.validation_level == ValidationLevel.HIGH else \
                             "○" if metric.validation_level == ValidationLevel.MEDIUM else \
                             "⚠" if metric.validation_level == ValidationLevel.LOW else "❌"
            lines.append(f"  {level_indicator} {metric.name}: {metric.score:.1f}/100 (Weight: {metric.weight:.0%})")
            lines.append(f"    {metric.description}")
        
        # Technical context
        lines.append(f"\n📈 TECHNICAL CONTEXT:")
        for key, value in analysis_result.technical_context.items():
            if isinstance(value, float):
                if 'volatility' in key:
                    lines.append(f"  {key.replace('_', ' ').title()}: {value:.2%}")
                else:
                    lines.append(f"  {key.replace('_', ' ').title()}: {value:.2f}")
            else:
                lines.append(f"  {key.replace('_', ' ').title()}: {value}")
        
        # Support/Resistance levels
        if analysis_result.support_levels:
            lines.append(f"\n📊 SUPPORT LEVELS:")
            for level in analysis_result.support_levels[:3]:
                lines.append(f"  • ${level:.2f}")
                
        if analysis_result.resistance_levels:
            lines.append(f"\n📊 RESISTANCE LEVELS:")
            for level in analysis_result.resistance_levels[:3]:
                lines.append(f"  • ${level:.2f}")
        
        # Self-explanation
        lines.append(f"\n🧠 SELF-EXPLANATION:")
        lines.append(validation_result.self_explanation)
        
        # Truth verification
        lines.append(f"\n✅ TRUTH VERIFICATION:")
        lines.append(validation_result.truth_verification)
        
        # Footer
        lines.append("\n" + "=" * 60)
        lines.append("⚠️  DISCLOSURE: This analysis is algorithmically generated and should not be")
        lines.append("    considered financial advice. Always validate with additional research")
        lines.append("    and consider your risk tolerance before making investment decisions.")
        lines.append("")
        lines.append("💡 This analysis includes self-evaluation and truth verification to")
        lines.append("    provide transparency about its own confidence and reliability.")
        
        return "\n".join(lines)

# Example usage
async def main():
    """Example usage of the enhanced evaluator"""
    print("🤖 Enhanced Analysis Evaluator Demo")
    print("=" * 50)
    
    # Create sample analysis result
    sample_result = AnalysisResult(
        symbol="NVDA",
        current_price=177.82,
        targets={
            "conservative": 182.05,
            "moderate": 188.39,
            "aggressive": 198.96,
            "stop_loss": 173.59
        },
        technical_context={
            "trend_direction": "sideways",
            "trend_strength": 0.38,
            "volatility": 0.0472
        },
        support_levels=[173.59, 126.46, 122.86],
        resistance_levels=[182.05, 188.39, 198.96]
    )
    
    # Create evaluator and template
    evaluator = EnhancedAnalysisEvaluator()
    template = EnhancedAnalysisResponseTemplate()
    
    # Evaluate the analysis
    validation_result = evaluator.evaluate(sample_result)
    
    # Format the response
    response = template.format_response(sample_result, validation_result)
    
    # Print the response
    print(response)

if __name__ == "__main__":
    asyncio.run(main())