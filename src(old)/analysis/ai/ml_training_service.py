"""
ML Training Service

Provides on-demand and scheduled training for the trading ML models.
Ensures models are trained and ready for production use.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import os
import json

from src.analysis.ai.ml_models import trading_ml_model
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)


class MLTrainingService:
    """
    Service for managing ML model training lifecycle.
    
    Features:
    - On-demand training with sample data
    - Scheduled retraining
    - Training status monitoring
    - Performance tracking
    """
    
    def __init__(self):
        self.training_in_progress = False
        self.last_training_time = None
        self.training_history = []
        self.training_config = {
            'default_symbols': ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX', 'ORCL', 'CRM'],
            'min_training_samples': 50,
            'retrain_interval_hours': 24,
            'max_history_entries': 10
        }
        
        logger.info("✅ ML Training Service initialized")
    
    async def ensure_models_trained(self) -> Dict[str, Any]:
        """
        Ensure ML models are trained and ready for use.
        
        This is the main entry point for activating ML capabilities.
        Checks if models are trained, and trains them if needed.
        
        Returns:
            Status of model training
        """
        try:
            # Check if models are already trained
            if trading_ml_model.is_trained:
                logger.info("✅ ML models already trained and ready")
                return {
                    'status': 'already_trained',
                    'is_trained': True,
                    'models_available': list(trading_ml_model.models.keys()),
                    'last_training': self.last_training_time.isoformat() if self.last_training_time else None
                }
            
            # Check if training is in progress
            if self.training_in_progress:
                logger.info("⏳ ML training already in progress")
                return {
                    'status': 'training_in_progress',
                    'is_trained': False,
                    'message': 'Training is currently in progress'
                }
            
            # Train the models
            logger.info("🚀 ML models not trained, starting training process")
            return await self.train_models_now()
            
        except Exception as e:
            logger.error(f"Error ensuring models are trained: {e}")
            return {
                'status': 'error',
                'is_trained': False,
                'error': str(e)
            }
    
    async def train_models_now(self, symbols: List[str] = None) -> Dict[str, Any]:
        """
        Train ML models immediately with sample data.
        
        Args:
            symbols: Optional list of symbols to train on
            
        Returns:
            Training results
        """
        if self.training_in_progress:
            return {
                'status': 'training_in_progress',
                'message': 'Training is already in progress'
            }
        
        self.training_in_progress = True
        training_start = datetime.now()
        
        try:
            logger.info("🧠 Starting ML model training")
            
            # Use default symbols if none provided
            if symbols is None:
                symbols = self.training_config['default_symbols']
            
            # Train the models
            training_result = await trading_ml_model.train_on_sample_data(symbols)
            
            # Update training history
            self.last_training_time = training_start
            training_duration = (datetime.now() - training_start).total_seconds()
            
            training_record = {
                'timestamp': training_start.isoformat(),
                'duration_seconds': training_duration,
                'symbols_used': symbols,
                'success': training_result.get('success', False),
                'models_trained': training_result.get('models_trained', []),
                'training_samples': training_result.get('training_samples', 0),
                'performance': training_result.get('performance', {})
            }
            
            self.training_history.append(training_record)
            
            # Keep only recent history
            if len(self.training_history) > self.training_config['max_history_entries']:
                self.training_history = self.training_history[-self.training_config['max_history_entries']:]
            
            if training_result.get('success'):
                logger.info(f"✅ ML training completed successfully in {training_duration:.2f}s")
                logger.info(f"   📊 Models trained: {training_result.get('models_trained', [])}")
                logger.info(f"   📈 Training samples: {training_result.get('training_samples', 0)}")
                
                return {
                    'status': 'training_completed',
                    'success': True,
                    'is_trained': trading_ml_model.is_trained,
                    'duration_seconds': training_duration,
                    **training_result
                }
            else:
                logger.error(f"❌ ML training failed: {training_result.get('error', 'Unknown error')}")
                return {
                    'status': 'training_failed',
                    'success': False,
                    'is_trained': False,
                    'error': training_result.get('error', 'Training failed'),
                    'duration_seconds': training_duration
                }
                
        except Exception as e:
            training_duration = (datetime.now() - training_start).total_seconds()
            logger.error(f"❌ ML training failed with exception: {e}")
            return {
                'status': 'training_error',
                'success': False,
                'is_trained': False,
                'error': str(e),
                'duration_seconds': training_duration
            }
        finally:
            self.training_in_progress = False
    
    def get_training_status(self) -> Dict[str, Any]:
        """Get current training status and history"""
        model_summary = trading_ml_model.get_model_summary()
        
        return {
            'is_trained': model_summary['is_trained'],
            'training_in_progress': self.training_in_progress,
            'last_training_time': self.last_training_time.isoformat() if self.last_training_time else None,
            'models_available': model_summary['models_available'],
            'performance_metrics': model_summary['performance_metrics'],
            'training_history': self.training_history[-5:],  # Last 5 training sessions
            'config': self.training_config
        }
    
    def needs_retraining(self) -> bool:
        """Check if models need retraining based on time interval"""
        if not self.last_training_time:
            return True
        
        time_since_training = datetime.now() - self.last_training_time
        retrain_interval = timedelta(hours=self.training_config['retrain_interval_hours'])
        
        return time_since_training > retrain_interval
    
    async def auto_retrain_if_needed(self) -> Optional[Dict[str, Any]]:
        """Automatically retrain models if needed"""
        if self.needs_retraining() and not self.training_in_progress:
            logger.info("🔄 Auto-retraining ML models")
            return await self.train_models_now()
        return None


# Global training service instance
ml_training_service = MLTrainingService()
