import logging
from typing import Optional, Dict, Any
import pandas as pd

from src.data.models.stock_data import FundamentalMetrics

logger = logging.getLogger(__name__)

class FundamentalMetricsCalculator:
    """Calculate fundamental analysis metrics"""
    
    @staticmethod
    def analyze_metrics(fundamentals: Dict[str, Any]) -> FundamentalMetrics:
        """Analyze fundamental metrics and create structured result"""
        try:
            # Extract metrics from fundamentals data
            pe_ratio = fundamentals.get('pe_ratio')
            eps = fundamentals.get('eps')
            revenue_growth = fundamentals.get('revenue_growth')
            profit_margin = fundamentals.get('profit_margin')
            debt_to_equity = fundamentals.get('debt_to_equity')
            return_on_equity = fundamentals.get('return_on_equity')
            price_to_book = fundamentals.get('price_to_book')
            peg_ratio = fundamentals.get('peg_ratio')
            dividend_yield = fundamentals.get('dividend_yield')
            free_cash_flow = fundamentals.get('free_cash_flow')
            
            return FundamentalMetrics(
                pe_ratio=pe_ratio,
                eps=eps,
                revenue_growth=revenue_growth,
                profit_margin=profit_margin,
                debt_to_equity=debt_to_equity,
                return_on_equity=return_on_equity,
                price_to_book=price_to_book,
                peg_ratio=peg_ratio,
                dividend_yield=dividend_yield,
                free_cash_flow=free_cash_flow
            )
            
        except Exception as e:
            logger.error(f"Fundamental metrics analysis error: {e}")
            return FundamentalMetrics()
    
    @staticmethod
    def get_pe_status(pe_ratio: Optional[float]) -> str:
        """Get P/E ratio status"""
        if pe_ratio is None:
            return "UNKNOWN"
        elif pe_ratio < 15:
            return "UNDERVAULED"
        elif pe_ratio < 25:
            return "FAIR"
        else:
            return "OVERVALUED"
    
    @staticmethod
    def get_growth_status(growth_rate: Optional[float]) -> str:
        """Get growth rate status"""
        if growth_rate is None:
            return "UNKNOWN"
        elif growth_rate > 0.15:  # 15%
            return "HIGH_GROWTH"
        elif growth_rate > 0.05:  # 5%
            return "MODERATE_GROWTH"
        elif growth_rate > 0:
            return "LOW_GROWTH"
        else:
            return "DECLINING"
    
    @staticmethod
    def get_margin_status(profit_margin: Optional[float]) -> str:
        """Get profit margin status"""
        if profit_margin is None:
            return "UNKNOWN"
        elif profit_margin > 0.20:  # 20%
            return "EXCELLENT"
        elif profit_margin > 0.10:  # 10%
            return "GOOD"
        elif profit_margin > 0:
            return "FAIR"
        else:
            return "LOSS_MAKING"
    
    @staticmethod
    def get_debt_status(debt_to_equity: Optional[float]) -> str:
        """Get debt-to-equity status"""
        if debt_to_equity is None:
            return "UNKNOWN"
        elif debt_to_equity < 0.3:
            return "LOW_DEBT"
        elif debt_to_equity < 0.8:
            return "MODERATE_DEBT"
        elif debt_to_equity < 1.5:
            return "HIGH_DEBT"
        else:
            return "VERY_HIGH_DEBT"
    
    @staticmethod
    def get_roe_status(return_on_equity: Optional[float]) -> str:
        """Get ROE status"""
        if return_on_equity is None:
            return "UNKNOWN"
        elif return_on_equity > 0.20:  # 20%
            return "EXCELLENT"
        elif return_on_equity > 0.15:  # 15%
            return "GOOD"
        elif return_on_equity > 0.10:  # 10%
            return "FAIR"
        else:
            return "POOR"
    
    @staticmethod
    def get_pb_status(price_to_book: Optional[float]) -> str:
        """Get P/B ratio status"""
        if price_to_book is None:
            return "UNKNOWN"
        elif price_to_book < 1.0:
            return "UNDERVAULED"
        elif price_to_book < 3.0:
            return "FAIR"
        else:
            return "OVERVALUED"
    
    @staticmethod
    def get_peg_status(peg_ratio: Optional[float]) -> str:
        """Get PEG ratio status"""
        if peg_ratio is None:
            return "UNKNOWN"
        elif peg_ratio < 1.0:
            return "UNDERVAULED"
        elif peg_ratio < 2.0:
            return "FAIR"
        else:
            return "OVERVALUED"
    
    @staticmethod
    def get_yield_status(dividend_yield: Optional[float]) -> str:
        """Get dividend yield status"""
        if dividend_yield is None:
            return "UNKNOWN"
        elif dividend_yield > 0.04:  # 4%
            return "HIGH_YIELD"
        elif dividend_yield > 0.02:  # 2%
            return "MODERATE_YIELD"
        elif dividend_yield > 0:
            return "LOW_YIELD"
        else:
            return "NO_DIVIDEND"
