from typing import Dict, Optional
import logging
import numpy as np
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class PERatioAnalysis:
    """Data class for P/E ratio analysis results"""
    pe_ratio: Optional[float]
    industry_pe: Optional[float]
    sector_pe: Optional[float]
    pe_percentile: Optional[float]
    valuation_status: str
    confidence: float

class PECalculator:
    """Calculator for Price-to-Earnings (P/E) ratio analysis"""
    
    def __init__(self):
        # Industry and sector average P/E ratios (placeholders)
        self.industry_benchmarks = {
            "technology": 25.0,
            "healthcare": 20.0,
            "financial": 15.0,
            "consumer": 18.0,
            "industrial": 16.0,
            "energy": 12.0
        }
    
    def calculate_pe_ratio(self, price: float, earnings_per_share: float) -> Optional[float]:
        """
        Calculate basic P/E ratio
        
        Args:
            price: Current stock price
            earnings_per_share: Earnings per share (EPS)
            
        Returns:
            P/E ratio or None if invalid
        """
        if earnings_per_share <= 0 or price <= 0:
            return None
            
        return price / earnings_per_share
    
    def analyze_pe_ratio(self, 
                        price: float, 
                        earnings_per_share: float, 
                        industry: str = None,
                        sector: str = None) -> PERatioAnalysis:
        """
        Comprehensive P/E ratio analysis
        
        Args:
            price: Current stock price
            earnings_per_share: Earnings per share
            industry: Industry classification
            sector: Sector classification
            
        Returns:
            PERatioAnalysis object with detailed analysis
        """
        pe_ratio = self.calculate_pe_ratio(price, earnings_per_share)
        
        if pe_ratio is None:
            return PERatioAnalysis(
                pe_ratio=None,
                industry_pe=None,
                sector_pe=None,
                pe_percentile=None,
                valuation_status="N/A",
                confidence=0.0
            )
        
        # Get industry and sector benchmarks
        industry_pe = self.industry_benchmarks.get(industry.lower(), 20.0) if industry else 20.0
        sector_pe = self.industry_benchmarks.get(sector.lower(), 18.0) if sector else 18.0
        
        # Calculate percentile (simplified)
        pe_percentile = self._calculate_pe_percentile(pe_ratio, industry)
        
        # Determine valuation status
        valuation_status = self._determine_valuation_status(pe_ratio, industry_pe)
        
        # Calculate confidence
        confidence = self._calculate_confidence(pe_ratio, earnings_per_share)
        
        return PERatioAnalysis(
            pe_ratio=pe_ratio,
            industry_pe=industry_pe,
            sector_pe=sector_pe,
            pe_percentile=pe_percentile,
            valuation_status=valuation_status,
            confidence=confidence
        )
    
    def _calculate_pe_percentile(self, pe_ratio: float, industry: str) -> float:
        """
        Calculate P/E ratio percentile within industry
        
        Args:
            pe_ratio: Current P/E ratio
            industry: Industry classification
            
        Returns:
            Percentile (0-1)
        """
        # Simplified percentile calculation
        # In production, this would use historical industry data
        industry_avg = self.industry_benchmarks.get(industry.lower(), 20.0)
        
        if pe_ratio <= industry_avg * 0.7:
            return 0.2  # Low P/E
        elif pe_ratio <= industry_avg:
            return 0.4  # Below average
        elif pe_ratio <= industry_avg * 1.3:
            return 0.6  # Average
        elif pe_ratio <= industry_avg * 1.7:
            return 0.8  # Above average
        else:
            return 0.95  # High P/E
    
    def _determine_valuation_status(self, pe_ratio: float, industry_pe: float) -> str:
        """
        Determine valuation status based on P/E ratio
        
        Args:
            pe_ratio: Current P/E ratio
            industry_pe: Industry average P/E
            
        Returns:
            Valuation status string
        """
        if pe_ratio < industry_pe * 0.7:
            return "Undervalued"
        elif pe_ratio < industry_pe * 0.9:
            return "Slightly Undervalued"
        elif pe_ratio <= industry_pe * 1.1:
            return "Fairly Valued"
        elif pe_ratio <= industry_pe * 1.3:
            return "Slightly Overvalued"
        else:
            return "Overvalued"
    
    def _calculate_confidence(self, pe_ratio: float, earnings_per_share: float) -> float:
        """
        Calculate analysis confidence
        
        Args:
            pe_ratio: P/E ratio
            earnings_per_share: EPS
            
        Returns:
            Confidence score (0-1)
        """
        confidence = 0.8  # Base confidence
        
        # Adjust based on earnings quality
        if earnings_per_share > 1.0:
            confidence += 0.1
        elif earnings_per_share > 0.5:
            confidence += 0.05
            
        # Adjust based on P/E ratio reasonableness
        if 5 <= pe_ratio <= 50:
            confidence += 0.1
            
        return min(1.0, max(0.0, confidence))

# Factory function
def create_pe_calculator() -> PECalculator:
    """Create and return a PECalculator instance"""
    return PECalculator()