"""
Probability Response Service

This service integrates Monte Carlo simulation with response generation
to provide probabilistic analysis for trading queries.
"""

import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import asyncio

from .monte_carlo_simulator import MonteCarloSimulator, MonteCarloResult
from .probability_engine import ProbabilityEngine
from src.shared.technical_analysis.calculator import TechnicalAnalysisCalculator

logger = logging.getLogger(__name__)

class ProbabilityResponseService:
    """Service for generating probability-based trading responses"""
    
    def __init__(self):
        self.monte_carlo = MonteCarloSimulator()
        self.probability_engine = ProbabilityEngine()
        self.technical_calculator = TechnicalAnalysisCalculator()
        self.logger = logger
    
    async def analyze_price_probability(self,
                                      symbol: str,
                                      current_price: float,
                                      target_price: Optional[float] = None,
                                      target_percentage: Optional[float] = None,
                                      time_horizon_days: int = 5,
                                      historical_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analyze probability of price movements using Monte Carlo simulation
        
        Args:
            symbol: Stock symbol
            current_price: Current price
            target_price: Specific target price (optional)
            target_percentage: Target percentage change (optional)
            time_horizon_days: Days to analyze
            historical_data: Historical price data
            
        Returns:
            Dictionary with probability analysis results
        """
        try:
            # Calculate target price if percentage provided
            if target_price is None and target_percentage is not None:
                target_price = current_price * (1 + target_percentage / 100)
            elif target_price is None:
                # Default to 5% gain for analysis
                target_price = current_price * 1.05
            
            # Convert historical data to DataFrame if needed
            if historical_data is not None and isinstance(historical_data, dict):
                import pandas as pd
                historical_df = pd.DataFrame(historical_data)
            elif historical_data is not None and hasattr(historical_data, 'empty'):
                # Already a DataFrame
                historical_df = historical_data
            else:
                # Use default historical data structure
                historical_df = self._create_default_historical_data(current_price)
            
            # Calculate technical indicators
            technical_indicators = await self._calculate_technical_indicators(historical_df)
            
            # Run Monte Carlo analysis
            monte_carlo_result = await self.monte_carlo.run_analysis(
                symbol=symbol,
                current_price=current_price,
                target_price=target_price,
                historical_data=historical_df,
                time_horizon_days=time_horizon_days,
                technical_indicators=technical_indicators
            )
            
            # Format results for response template
            return self._format_probability_response(monte_carlo_result, technical_indicators)
            
        except Exception as e:
            self.logger.error(f"Error in probability analysis for {symbol}: {e}")
            return self._create_error_response(symbol, current_price, target_price)
    
    async def _calculate_technical_indicators(self, historical_data) -> Dict[str, Any]:
        """Calculate technical indicators for the analysis"""
        try:
            if historical_data.empty or len(historical_data) < 20:
                return {}
            
            # Extract price data
            if 'close' in historical_data.columns:
                prices = historical_data['close']
            else:
                prices = historical_data.iloc[:, -1]
            
            # Calculate basic indicators
            indicators = {}
            
            # RSI
            if len(prices) >= 14:
                rsi = self.technical_calculator.calculate_rsi(prices, 14)
                if rsi is not None:
                    indicators['rsi'] = rsi
            
            # MACD
            if len(prices) >= 26:
                macd_result = self.technical_calculator.calculate_macd(prices, 12, 26, 9)
                if macd_result:
                    indicators['macd'] = macd_result.get('macd', 0)
                    indicators['macd_signal'] = macd_result.get('signal', 0)
                    indicators['macd_histogram'] = macd_result.get('histogram', 0)
            
            # Moving averages
            if len(prices) >= 20:
                sma_20 = prices.rolling(20).mean().iloc[-1]
                sma_50 = prices.rolling(50).mean().iloc[-1] if len(prices) >= 50 else None
                indicators['sma_20'] = sma_20
                if sma_50 is not None:
                    indicators['sma_50'] = sma_50
            
            # Bollinger Bands
            if len(prices) >= 20:
                bb_result = self.technical_calculator.calculate_bollinger_bands(prices, 20, 2)
                if bb_result:
                    indicators['bb_upper'] = bb_result.get('upper', 0)
                    indicators['bb_lower'] = bb_result.get('lower', 0)
                    indicators['bb_middle'] = bb_result.get('middle', 0)
            
            return indicators
            
        except Exception as e:
            self.logger.error(f"Error calculating technical indicators: {e}")
            return {}
    
    def _format_probability_response(self, 
                                   result: MonteCarloResult, 
                                   technical_indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Format Monte Carlo result for response template"""
        try:
            # Format Monte Carlo results
            monte_carlo_results = f"""
• **Simulation Runs:** {result.simulation_runs:,}
• **Expected Price:** ${result.mean_expected_price:.2f} (±${result.std_expected_price:.2f})
• **Confidence Intervals:**
  - 50%: ${result.confidence_intervals['50%']:.2f}
  - 90%: ${result.confidence_intervals['90%']:.2f}
  - 95%: ${result.confidence_intervals['95%']:.2f}
• **Value at Risk (95%):** ${result.value_at_risk_95:.2f}
• **Max Drawdown Risk:** {result.max_drawdown_probability:.1%}
• **Volatility Forecast:** {result.volatility_forecast:.1%} daily"""
            
            # Format technical indicators used
            indicators_used = []
            if 'rsi' in technical_indicators:
                rsi = technical_indicators['rsi']
                indicators_used.append(f"RSI: {rsi:.1f}")
            if 'macd' in technical_indicators:
                macd = technical_indicators['macd']
                signal = technical_indicators.get('macd_signal', 0)
                indicators_used.append(f"MACD: {macd:.3f} (Signal: {signal:.3f})")
            if 'sma_20' in technical_indicators:
                sma_20 = technical_indicators['sma_20']
                indicators_used.append(f"SMA(20): ${sma_20:.2f}")
            if 'bb_upper' in technical_indicators:
                bb_upper = technical_indicators['bb_upper']
                bb_lower = technical_indicators['bb_lower']
                indicators_used.append(f"Bollinger Bands: ${bb_lower:.2f} - ${bb_upper:.2f}")
            
            indicators_text = "\n".join([f"• {ind}" for ind in indicators_used]) if indicators_used else "• Basic price analysis"
            
            # Format risk factors
            risk_factors = []
            if result.max_drawdown_probability > 0.15:
                risk_factors.append(f"High drawdown risk ({result.max_drawdown_probability:.1%})")
            if result.volatility_forecast > 0.03:
                risk_factors.append(f"High volatility expected ({result.volatility_forecast:.1%} daily)")
            if result.value_at_risk_95 > result.current_price * 0.1:
                risk_factors.append(f"Significant downside risk (${result.value_at_risk_95:.2f})")
            
            risk_text = "\n".join([f"• {risk}" for risk in risk_factors]) if risk_factors else "• Standard market risks"
            
            # Format target analysis
            price_change = ((result.target_price - result.current_price) / result.current_price) * 100
            target_analysis = f"Target: ${result.target_price:.2f} ({price_change:+.1f}% from current ${result.current_price:.2f})"
            
            return {
                'target_analysis': target_analysis,
                'monte_carlo_results': monte_carlo_results,
                'bullish_probability': f"{result.probability_bullish:.1%}",
                'bearish_probability': f"{result.probability_bearish:.1%}",
                'neutral_probability': f"{result.probability_neutral:.1%}",
                'indicators_used': indicators_text,
                'risk_factors': risk_text,
                'key_insights': "\n".join([f"• {insight}" for insight in result.key_insights]),
                'simulation_runs': f"{result.simulation_runs:,}",
                'confidence': f"{result.probability_of_reaching_target:.1%}",
                'probability_of_reaching_target': result.probability_of_reaching_target,
                'mean_expected_price': result.mean_expected_price,
                'volatility_forecast': result.volatility_forecast,
                'max_drawdown_probability': result.max_drawdown_probability
            }
            
        except Exception as e:
            self.logger.error(f"Error formatting probability response: {e}")
            return self._create_error_response(result.symbol, result.current_price, result.target_price)
    
    def _create_default_historical_data(self, current_price: float, allow_synthetic: bool = False):
        """
        Create default historical data for analysis

        Args:
            current_price: Current stock price
            allow_synthetic: Whether to allow synthetic data generation (dev/test only)
        """
        import pandas as pd
        import numpy as np
        import os

        environment = os.getenv('ENVIRONMENT', 'production').lower()

        # Only allow synthetic data in development/test environments or when explicitly allowed
        if not allow_synthetic and environment in ['production', 'prod']:
            self.logger.error("Attempted to create synthetic historical data in production")
            return pd.DataFrame()  # Return empty DataFrame

        if environment in ['production', 'prod'] and not allow_synthetic:
            self.logger.warning("Synthetic historical data disabled in production")
            return pd.DataFrame()

        self.logger.warning(f"Creating synthetic historical data in {environment} environment")

        # Generate 100 days of synthetic historical data
        dates = pd.date_range(end=datetime.now(), periods=100, freq='D')
        # Simple random walk with slight upward bias
        returns = np.random.normal(0.0005, 0.02, 100)  # 0.05% daily return, 2% volatility
        prices = [current_price]

        for ret in returns:
            prices.append(prices[-1] * (1 + ret))

        return pd.DataFrame({
            'date': dates,
            'close': prices[1:],  # Skip first price
            'open': [p * (1 + np.random.normal(0, 0.005)) for p in prices[1:]],
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices[1:]],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices[1:]],
            'volume': np.random.randint(1000000, 10000000, 100)
        })
    
    def _create_error_response(self, symbol: str, current_price: float, target_price: float) -> Dict[str, Any]:
        """Create error response when analysis fails"""
        return {
            'target_analysis': f"Target: ${target_price:.2f} (Analysis unavailable)",
            'monte_carlo_results': "• Analysis temporarily unavailable\n• Please try again later",
            'bullish_probability': "N/A",
            'bearish_probability': "N/A", 
            'neutral_probability': "N/A",
            'indicators_used': "• Analysis unavailable",
            'risk_factors': "• Unable to assess risks",
            'key_insights': "• Analysis failed - insufficient data or technical error",
            'simulation_runs': "0",
            'confidence': "N/A",
            'probability_of_reaching_target': 0.5,
            'mean_expected_price': current_price,
            'volatility_forecast': 0.02,
            'max_drawdown_probability': 0.1
        }
