"""
Probability Assessment Engine

Provides comprehensive probability analysis including:
- Statistical probability models
- Risk-adjusted return calculations
- Market regime detection
- Sentiment correlation analysis
- Historical accuracy tracking
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import logging
from datetime import datetime, timedelta
import statistics

logger = logging.getLogger(__name__)

class MarketRegime(Enum):
    """Market regime enumeration"""
    BULL_MARKET = "bull_market"
    BEAR_MARKET = "bear_market"
    SIDEWAYS_MARKET = "sideways_market"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    TRENDING = "trending"
    RANGING = "ranging"

class ProbabilityLevel(Enum):
    """Probability level enumeration"""
    VERY_LOW = "very_low"      # 0-20%
    LOW = "low"                # 20-40%
    MEDIUM = "medium"          # 40-60%
    HIGH = "high"              # 60-80%
    VERY_HIGH = "very_high"    # 80-100%

@dataclass
class ProbabilityAssessment:
    """Container for probability analysis results"""
    symbol: str
    timeframe: str
    bullish_probability: float      # 0.0 to 1.0
    bearish_probability: float      # 0.0 to 1.0
    sideways_probability: float     # 0.0 to 1.0
    confidence_level: float         # How certain we are (0.0 to 1.0)
    supporting_factors: List[str]   # Technical/fundamental evidence
    risk_factors: List[str]         # Risk considerations
    market_regime: MarketRegime     # Current market regime
    regime_confidence: float        # Confidence in regime classification
    risk_adjusted_return: float     # Risk-adjusted return expectation
    historical_accuracy: float      # Historical prediction accuracy
    timestamp: str

class StatisticalModels:
    """Statistical probability models for market analysis"""
    
    def calculate_normal_probability(self, current_value: float, mean: float, 
                                   std_dev: float, target_value: float) -> float:
        """
        Calculate probability using normal distribution
        
        Args:
            current_value: Current market value
            mean: Historical mean
            std_dev: Historical standard deviation
            target_value: Target value to reach
            
        Returns:
            Probability of reaching target (0.0 to 1.0)
        """
        try:
            if std_dev == 0:
                return 0.5 if current_value == mean else 0.0
            
            # Calculate z-score
            z_score = (target_value - mean) / std_dev
            
            # Use normal distribution CDF approximation
            # This is a simplified version - in production use scipy.stats.norm.cdf
            if z_score < -3:
                return 0.001
            elif z_score > 3:
                return 0.999
            else:
                # Simplified normal CDF approximation
                return 0.5 * (1 + np.tanh(z_score / np.sqrt(2)))
                
        except Exception as e:
            logger.error(f"Error calculating normal probability: {e}")
            return 0.5
    
    def calculate_binomial_probability(self, success_rate: float, 
                                     trials: int, successes: int) -> float:
        """
        Calculate binomial probability
        
        Args:
            success_rate: Historical success rate
            trials: Number of trials
            successes: Number of successes
            
        Returns:
            Probability of current success rate
        """
        try:
            if trials == 0 or success_rate <= 0 or success_rate >= 1:
                return 0.5
            
            # Simplified binomial calculation
            expected_successes = trials * success_rate
            variance = trials * success_rate * (1 - success_rate)
            
            if variance == 0:
                return 0.5
            
            # Calculate how many standard deviations away from expected
            z_score = (successes - expected_successes) / np.sqrt(variance)
            
            # Convert to probability
            return max(0.0, min(1.0, 0.5 * (1 + np.tanh(z_score / 2))))
            
        except Exception as e:
            logger.error(f"Error calculating binomial probability: {e}")
            return 0.5
    
    def calculate_trend_probability(self, price_data: pd.DataFrame, 
                                  trend_direction: str) -> float:
        """
        Calculate probability of trend continuation
        
        Args:
            price_data: Historical price data
            trend_direction: Expected trend direction
            
        Returns:
            Probability of trend continuation
        """
        try:
            if len(price_data) < 20:
                return 0.5
            
            # Calculate trend strength indicators
            close_prices = price_data['close']
            
            # Calculate moving averages
            sma_20 = close_prices.rolling(window=20).mean()
            sma_50 = close_prices.rolling(window=50).mean()
            
            # Calculate trend consistency
            trend_consistency = 0
            total_periods = 0
            
            for i in range(20, len(close_prices)):
                if trend_direction == "bullish":
                    if close_prices.iloc[i] > sma_20.iloc[i] > sma_50.iloc[i]:
                        trend_consistency += 1
                elif trend_direction == "bearish":
                    if close_prices.iloc[i] < sma_20.iloc[i] < sma_50.iloc[i]:
                        trend_consistency += 1
                
                total_periods += 1
            
            if total_periods == 0:
                return 0.5
            
            # Convert to probability
            trend_probability = trend_consistency / total_periods
            
            # Adjust based on recent performance
            recent_trend = trend_consistency / min(total_periods, 10)
            
            # Weighted average (70% recent, 30% overall)
            final_probability = (0.7 * recent_trend) + (0.3 * trend_probability)
            
            return max(0.1, min(0.9, final_probability))
            
        except Exception as e:
            logger.error(f"Error calculating trend probability: {e}")
            return 0.5

class RiskAdjustedCalculator:
    """Calculates risk-adjusted returns and probabilities"""
    
    def calculate_sharpe_ratio(self, returns: pd.Series, risk_free_rate: float = 0.02) -> float:
        """
        Calculate Sharpe ratio for risk-adjusted returns
        
        Args:
            returns: Series of returns
            risk_free_rate: Risk-free rate (default 2%)
            
        Returns:
            Sharpe ratio
        """
        try:
            if len(returns) < 2:
                return 0.0
            
            excess_returns = returns - risk_free_rate
            if excess_returns.std() == 0:
                return 0.0
            
            return excess_returns.mean() / excess_returns.std()
            
        except Exception as e:
            logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0.0
    
    def calculate_max_drawdown(self, prices: pd.Series) -> float:
        """
        Calculate maximum drawdown
        
        Args:
            prices: Series of prices
            
        Returns:
            Maximum drawdown percentage
        """
        try:
            if len(prices) < 2:
                return 0.0
            
            # Calculate running maximum
            running_max = prices.expanding().max()
            
            # Calculate drawdown
            drawdown = (prices - running_max) / running_max
            
            return abs(drawdown.min())
            
        except Exception as e:
            logger.error(f"Error calculating max drawdown: {e}")
            return 0.0
    
    def calculate_risk_adjusted_probability(self, expected_return: float, 
                                          volatility: float, 
                                          max_drawdown: float) -> float:
        """
        Calculate probability adjusted for risk factors
        
        Args:
            expected_return: Expected return
            volatility: Price volatility
            max_drawdown: Maximum drawdown
            
        Returns:
            Risk-adjusted probability
        """
        try:
            # Base probability from expected return
            base_probability = 0.5 + (expected_return * 2)  # Scale to 0-1
            
            # Adjust for volatility (higher volatility = lower probability)
            volatility_penalty = min(0.3, volatility * 2)
            base_probability -= volatility_penalty
            
            # Adjust for drawdown risk (higher drawdown = lower probability)
            drawdown_penalty = min(0.2, max_drawdown * 2)
            base_probability -= drawdown_penalty
            
            # Ensure probability stays in valid range
            return max(0.1, min(0.9, base_probability))
            
        except Exception as e:
            logger.error(f"Error calculating risk-adjusted probability: {e}")
            return 0.5

class MarketRegimeDetector:
    """Detects current market regime for probability adjustment"""
    
    def detect_regime(self, price_data: pd.DataFrame, 
                     volume_data: pd.DataFrame) -> Tuple[MarketRegime, float]:
        """
        Detect current market regime
        
        Args:
            price_data: OHLCV price data
            volume_data: Volume data
            
        Returns:
            Tuple of (regime, confidence)
        """
        try:
            if price_data.empty:
                return MarketRegime.SIDEWAYS_MARKET, 0.5
            
            close_prices = price_data['close']
            
            # Calculate trend indicators
            sma_20 = close_prices.rolling(window=20).mean()
            sma_50 = close_prices.rolling(window=50).mean()
            
            # Calculate volatility
            volatility = close_prices.pct_change().std()
            
            # Determine trend direction
            current_price = close_prices.iloc[-1]
            current_sma_20 = sma_20.iloc[-1]
            current_sma_50 = sma_50.iloc[-1]
            
            # Calculate trend strength
            if len(close_prices) >= 20:
                trend_strength = abs(current_price - close_prices.iloc[-20]) / close_prices.iloc[-20]
            else:
                trend_strength = 0.0
            
            # Determine regime
            if current_price > current_sma_20 > current_sma_50 and trend_strength > 0.05:
                regime = MarketRegime.BULL_MARKET
                confidence = min(0.9, 0.5 + (trend_strength * 2))
            elif current_price < current_sma_20 < current_sma_50 and trend_strength > 0.05:
                regime = MarketRegime.BEAR_MARKET
                confidence = min(0.9, 0.5 + (trend_strength * 2))
            elif volatility > 0.03:
                regime = MarketRegime.HIGH_VOLATILITY
                confidence = min(0.8, 0.4 + (volatility * 5))
            elif volatility < 0.01:
                regime = MarketRegime.LOW_VOLATILITY
                confidence = min(0.8, 0.6 + (0.01 - volatility) * 10)
            elif trend_strength > 0.02:
                regime = MarketRegime.TRENDING
                confidence = min(0.7, 0.5 + (trend_strength * 3))
            else:
                regime = MarketRegime.RANGING
                confidence = 0.6
            
            return regime, confidence
            
        except Exception as e:
            logger.error(f"Error detecting market regime: {e}")
            return MarketRegime.SIDEWAYS_MARKET, 0.5
    
    def adjust_probabilities_for_regime(self, probabilities: Dict[str, float], 
                                      regime: MarketRegime) -> Dict[str, float]:
        """
        Adjust probabilities based on market regime
        
        Args:
            probabilities: Base probabilities
            regime: Current market regime
            
        Returns:
            Regime-adjusted probabilities
        """
        try:
            adjusted = probabilities.copy()
            
            if regime == MarketRegime.BULL_MARKET:
                # Increase bullish probability
                adjusted['bullish'] = min(0.9, adjusted['bullish'] * 1.2)
                adjusted['bearish'] = max(0.1, adjusted['bearish'] * 0.8)
            elif regime == MarketRegime.BEAR_MARKET:
                # Increase bearish probability
                adjusted['bearish'] = min(0.9, adjusted['bearish'] * 1.2)
                adjusted['bullish'] = max(0.1, adjusted['bullish'] * 0.8)
            elif regime == MarketRegime.HIGH_VOLATILITY:
                # Increase sideways probability
                adjusted['sideways'] = min(0.6, adjusted['sideways'] * 1.3)
                adjusted['bullish'] = max(0.1, adjusted['bullish'] * 0.9)
                adjusted['bearish'] = max(0.1, adjusted['bearish'] * 0.9)
            elif regime == MarketRegime.LOW_VOLATILITY:
                # Increase trend probability
                if adjusted['bullish'] > adjusted['bearish']:
                    adjusted['bullish'] = min(0.9, adjusted['bullish'] * 1.1)
                else:
                    adjusted['bearish'] = min(0.9, adjusted['bearish'] * 1.1)
            
            # Normalize probabilities
            total = sum(adjusted.values())
            if total > 0:
                adjusted = {k: v/total for k, v in adjusted.items()}
            
            return adjusted
            
        except Exception as e:
            logger.error(f"Error adjusting probabilities for regime: {e}")
            return probabilities

class SentimentCorrelator:
    """Correlates sentiment data with probability calculations"""
    
    def correlate_sentiment(self, sentiment_score: float, 
                           base_probabilities: Dict[str, float]) -> Dict[str, float]:
        """
        Adjust probabilities based on sentiment
        
        Args:
            sentiment_score: Sentiment score (-1.0 to 1.0)
            base_probabilities: Base probability calculations
            
        Returns:
            Sentiment-adjusted probabilities
        """
        try:
            adjusted = base_probabilities.copy()
            
            # Sentiment score: -1.0 (very bearish) to 1.0 (very bullish)
            if sentiment_score > 0.3:  # Bullish sentiment
                adjusted['bullish'] = min(0.9, adjusted['bullish'] * (1 + sentiment_score * 0.3))
                adjusted['bearish'] = max(0.1, adjusted['bearish'] * (1 - sentiment_score * 0.3))
            elif sentiment_score < -0.3:  # Bearish sentiment
                adjusted['bearish'] = min(0.9, adjusted['bearish'] * (1 - sentiment_score * 0.3))
                adjusted['bullish'] = max(0.1, adjusted['bullish'] * (1 + sentiment_score * 0.3))
            
            # Normalize probabilities
            total = sum(adjusted.values())
            if total > 0:
                adjusted = {k: v/total for k, v in adjusted.items()}
            
            return adjusted
            
        except Exception as e:
            logger.error(f"Error correlating sentiment: {e}")
            return base_probabilities

class HistoricalAccuracyTracker:
    """Tracks historical prediction accuracy for probability validation"""
    
    def __init__(self):
        self.predictions = []
    
    def add_prediction(self, symbol: str, prediction: str, actual: str, 
                      confidence: float, timestamp: datetime):
        """Add a new prediction for tracking"""
        self.predictions.append({
            'symbol': symbol,
            'prediction': prediction,
            'actual': actual,
            'confidence': confidence,
            'timestamp': timestamp,
            'correct': prediction == actual
        })
    
    def calculate_accuracy(self, symbol: str = None, days: int = 30) -> float:
        """
        Calculate historical accuracy
        
        Args:
            symbol: Specific symbol to analyze (None for all)
            days: Number of days to look back
            
        Returns:
            Accuracy percentage (0.0 to 1.0)
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # Filter predictions
            if symbol:
                filtered = [p for p in self.predictions 
                           if p['symbol'] == symbol and p['timestamp'] >= cutoff_date]
            else:
                filtered = [p for p in self.predictions 
                           if p['timestamp'] >= cutoff_date]
            
            if not filtered:
                return 0.5  # Default accuracy if no data
            
            correct = sum(1 for p in filtered if p['correct'])
            return correct / len(filtered)
            
        except Exception as e:
            logger.error(f"Error calculating accuracy: {e}")
            return 0.5
    
    def get_confidence_calibration(self, symbol: str = None) -> float:
        """
        Calculate how well-calibrated confidence scores are
        
        Args:
            symbol: Specific symbol to analyze (None for all)
            
        Returns:
            Calibration score (1.0 = perfectly calibrated)
        """
        try:
            if symbol:
                predictions = [p for p in self.predictions if p['symbol'] == symbol]
            else:
                predictions = self.predictions
            
            if not predictions:
                return 1.0
            
            # Group by confidence level and calculate accuracy
            confidence_groups = {}
            for pred in predictions:
                conf_level = round(pred['confidence'], 1)
                if conf_level not in confidence_groups:
                    confidence_groups[conf_level] = []
                confidence_groups[conf_level].append(pred)
            
            # Calculate calibration
            total_error = 0
            total_predictions = 0
            
            for conf_level, group in confidence_groups.items():
                accuracy = sum(1 for p in group if p['correct']) / len(group)
                error = abs(conf_level - accuracy)
                total_error += error * len(group)
                total_predictions += len(group)
            
            if total_predictions == 0:
                return 1.0
            
            # Return calibration score (1.0 = perfect, 0.0 = terrible)
            return max(0.0, 1.0 - (total_error / total_predictions))
            
        except Exception as e:
            logger.error(f"Error calculating confidence calibration: {e}")
            return 1.0

class ProbabilityEngine:
    """Main engine for comprehensive probability analysis"""
    
    def __init__(self):
        self.statistical_models = StatisticalModels()
        self.risk_calculator = RiskAdjustedCalculator()
        self.regime_detector = MarketRegimeDetector()
        self.sentiment_correlator = SentimentCorrelator()
        self.accuracy_tracker = HistoricalAccuracyTracker()
    
    def assess_probabilities(self, symbol: str, timeframe: str, 
                           price_data: pd.DataFrame, volume_data: pd.DataFrame,
                           sentiment_score: float = 0.0) -> ProbabilityAssessment:
        """
        Assess comprehensive probabilities for a symbol
        
        Args:
            symbol: Stock symbol
            timeframe: Analysis timeframe
            price_data: OHLCV price data
            volume_data: Volume data
            sentiment_score: Sentiment score (-1.0 to 1.0)
            
        Returns:
            ProbabilityAssessment object
        """
        try:
            if price_data.empty:
                raise ValueError("Price data is empty")
            
            # Detect market regime
            regime, regime_confidence = self.regime_detector.detect_regime(
                price_data, volume_data
            )
            
            # Calculate base probabilities
            base_probabilities = self._calculate_base_probabilities(
                price_data, volume_data
            )
            
            # Adjust for market regime
            regime_adjusted = self.regime_detector.adjust_probabilities_for_regime(
                base_probabilities, regime
            )
            
            # Adjust for sentiment
            sentiment_adjusted = self.sentiment_correlator.correlate_sentiment(
                sentiment_score, regime_adjusted
            )
            
            # Calculate confidence level
            confidence_level = self._calculate_confidence_level(
                price_data, volume_data, regime_confidence
            )
            
            # Calculate risk-adjusted return
            risk_adjusted_return = self._calculate_risk_adjusted_return(
                price_data, sentiment_adjusted
            )
            
            # Get historical accuracy
            historical_accuracy = self.accuracy_tracker.calculate_accuracy(symbol)
            
            # Generate supporting and risk factors
            supporting_factors = self._generate_supporting_factors(
                sentiment_adjusted, regime, confidence_level
            )
            
            risk_factors = self._generate_risk_factors(
                price_data, volume_data, regime
            )
            
            return ProbabilityAssessment(
                symbol=symbol,
                timeframe=timeframe,
                bullish_probability=sentiment_adjusted['bullish'],
                bearish_probability=sentiment_adjusted['bearish'],
                sideways_probability=sentiment_adjusted['sideways'],
                confidence_level=confidence_level,
                supporting_factors=supporting_factors,
                risk_factors=risk_factors,
                market_regime=regime,
                regime_confidence=regime_confidence,
                risk_adjusted_return=risk_adjusted_return,
                historical_accuracy=historical_accuracy,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            logger.error(f"Error assessing probabilities for {symbol}: {e}")
            raise
    
    def _calculate_base_probabilities(self, price_data: pd.DataFrame, 
                                    volume_data: pd.DataFrame) -> Dict[str, float]:
        """Calculate base probabilities from technical analysis"""
        
        try:
            close_prices = price_data['close']
            
            # Calculate trend probability
            bullish_prob = self.statistical_models.calculate_trend_probability(
                price_data, "bullish"
            )
            bearish_prob = self.statistical_models.calculate_trend_probability(
                price_data, "bearish"
            )
            
            # Calculate volatility-based sideways probability
            volatility = close_prices.pct_change().std()
            sideways_prob = min(0.4, volatility * 5)  # Higher volatility = more sideways
            
            # Normalize probabilities
            total = bullish_prob + bearish_prob + sideways_prob
            if total > 0:
                return {
                    'bullish': bullish_prob / total,
                    'bearish': bearish_prob / total,
                    'sideways': sideways_prob / total
                }
            else:
                return {'bullish': 0.33, 'bearish': 0.33, 'sideways': 0.34}
                
        except Exception as e:
            logger.error(f"Error calculating base probabilities: {e}")
            return {'bullish': 0.33, 'bearish': 0.33, 'sideways': 0.34}
    
    def _calculate_confidence_level(self, price_data: pd.DataFrame, 
                                  volume_data: pd.DataFrame, 
                                  regime_confidence: float) -> float:
        """Calculate overall confidence level"""
        
        try:
            # Base confidence from data quality
            data_quality = min(1.0, len(price_data) / 100)  # More data = higher confidence
            
            # Volume confidence
            if not volume_data.empty:
                volume_confidence = min(1.0, volume_data['volume'].mean() / 1000000)  # Higher volume = higher confidence
            else:
                volume_confidence = 0.5
            
            # Combine confidence factors
            confidence = (data_quality * 0.4 + 
                         volume_confidence * 0.3 + 
                         regime_confidence * 0.3)
            
            return max(0.1, min(0.9, confidence))
            
        except Exception as e:
            logger.error(f"Error calculating confidence level: {e}")
            return 0.5
    
    def _calculate_risk_adjusted_return(self, price_data: pd.DataFrame, 
                                      probabilities: Dict[str, float]) -> float:
        """Calculate risk-adjusted return expectation"""
        
        try:
            # Calculate expected return
            expected_return = (probabilities['bullish'] * 0.1 +  # 10% upside
                             probabilities['bearish'] * -0.05 +  # 5% downside
                             probabilities['sideways'] * 0.02)   # 2% sideways
            
            # Calculate risk (volatility)
            volatility = price_data['close'].pct_change().std()
            
            # Calculate Sharpe ratio equivalent
            if volatility > 0:
                risk_adjusted = expected_return / volatility
            else:
                risk_adjusted = expected_return
            
            return round(risk_adjusted, 4)
            
        except Exception as e:
            logger.error(f"Error calculating risk-adjusted return: {e}")
            return 0.0
    
    def _generate_supporting_factors(self, probabilities: Dict[str, float], 
                                   regime: MarketRegime, 
                                   confidence: float) -> List[str]:
        """Generate supporting factors for the analysis"""
        
        factors = []
        
        # Probability-based factors
        if probabilities['bullish'] > 0.6:
            factors.append("Strong bullish probability")
        elif probabilities['bearish'] > 0.6:
            factors.append("Strong bearish probability")
        else:
            factors.append("Mixed signals - moderate probability")
        
        # Regime-based factors
        if regime == MarketRegime.BULL_MARKET:
            factors.append("Bull market regime")
        elif regime == MarketRegime.BEAR_MARKET:
            factors.append("Bear market regime")
        elif regime == MarketRegime.TRENDING:
            factors.append("Trending market conditions")
        
        # Confidence-based factors
        if confidence > 0.7:
            factors.append("High confidence analysis")
        elif confidence > 0.5:
            factors.append("Moderate confidence analysis")
        else:
            factors.append("Low confidence - exercise caution")
        
        return factors
    
    def _generate_risk_factors(self, price_data: pd.DataFrame, 
                              volume_data: pd.DataFrame, 
                              regime: MarketRegime) -> List[str]:
        """Generate risk factors for the analysis"""
        
        risk_factors = []
        
        # Volatility risk
        volatility = price_data['close'].pct_change().std()
        if volatility > 0.05:
            risk_factors.append("High volatility - increased risk")
        elif volatility < 0.01:
            risk_factors.append("Low volatility - limited opportunity")
        
        # Volume risk
        if not volume_data.empty:
            avg_volume = volume_data['volume'].mean()
            if avg_volume < 100000:
                risk_factors.append("Low volume - liquidity risk")
        
        # Regime risk
        if regime == MarketRegime.HIGH_VOLATILITY:
            risk_factors.append("High volatility regime")
        elif regime == MarketRegime.SIDEWAYS_MARKET:
            risk_factors.append("Sideways market - limited trend opportunities")
        
        # Data quality risk
        if len(price_data) < 50:
            risk_factors.append("Limited historical data")
        
        return risk_factors 