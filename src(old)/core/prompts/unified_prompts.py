"""
Unified Prompt Functions

This module provides unified prompt functions that can replace the scattered
prompt definitions throughout the codebase. These functions serve as the
single source of truth for all AI prompts.
"""

from typing import Dict, Any, Optional
from datetime import datetime

# Import centralized components
from .base.personas import PersonaManager
from .base.system_prompts import SystemPromptManager
from .base.compliance import ComplianceManager
from .commands.ask_prompts import Ask<PERSON><PERSON>mandPrompts
from .utils.context_injection import ContextInjector

# Global instances for unified access
_persona_manager = PersonaManager()
_system_prompt_manager = SystemPromptManager()
_compliance_manager = ComplianceManager()
_ask_prompts = AskCommandPrompts()
_context_injector = ContextInjector()

def get_system_prompt(persona: str = "trading_expert",
                     context: Optional[Dict[str, Any]] = None,
                     include_json_format: bool = True,
                     command: str = None) -> str:
    """
    Unified system prompt function to replace all scattered get_system_prompt() functions
    
    This function serves as the single source of truth for system prompts across
    the entire application, eliminating duplication and ensuring consistency.
    
    Args:
        persona: AI persona to use (trading_expert, risk_analyst, etc.)
        context: Additional context to inject (market data, user preferences, etc.)
        include_json_format: Whether to include JSON formatting requirements
        command: Specific command context (ask, analyze, etc.)
        
    Returns:
        Complete system prompt string
    """
    # Use command-specific prompts if available
    if command == "ask":
        return _ask_prompts.get_system_prompt(context)
    elif command and hasattr(_system_prompt_manager, f'get_{command}_system_prompt'):
        return getattr(_system_prompt_manager, f'get_{command}_system_prompt')(persona, context)
    else:
        # Use general system prompt
        return _system_prompt_manager.get_system_prompt(
            persona=persona,
            context=context,
            include_json_format=include_json_format
        )

def get_fallback_responses(command: str = "general") -> Dict[str, str]:
    """
    Unified fallback responses function
    
    Args:
        command: Command context for specific fallback responses
        
    Returns:
        Dictionary of fallback response templates
    """
    if command == "ask":
        return _ask_prompts.get_fallback_responses()
    else:
        # General fallback responses
        current_date = datetime.now().strftime("%B %d, %Y")
        return {
            "ai_error": f"""I apologize, but I'm experiencing technical difficulties processing your request right now ({current_date}). 

Please try again in a moment or rephrase your question. For immediate assistance, you can:
• Try a simpler query
• Check basic market information on your broker's platform
• Visit financial news websites for current market updates

⚠️ This is educational content, not financial advice. Always conduct your own research before making investment decisions.""",

            "no_ai_config": f"""I'm currently unable to provide AI-powered analysis due to configuration issues ({current_date}).

However, I can still help you with basic information and educational content. For real-time market data and personalized advice, please consult your broker or financial advisor.

⚠️ This is educational content, not financial advice. Trading involves substantial risk of loss.""",

            "timeout_error": f"""Your request is taking longer than expected to process ({current_date}). 

Please try:
• Simplifying your question
• Asking about fewer symbols at once
• Trying again in a moment

⚠️ This is educational content, not financial advice. For urgent trading decisions, consult real-time market data.""",

            "parsing_error": f"""I had trouble understanding your request ({current_date}). 

To get the best response, try:
• Using clear, specific questions
• Including stock symbols with $ prefix (e.g., $AAPL)
• Being specific about what type of information you want

⚠️ This is educational content, not financial advice. Always verify information with reliable sources."""
        }

def get_model_config_for_intent(intent: str) -> Dict[str, Any]:
    """
    Unified model configuration function
    
    Args:
        intent: Trading intent for configuration
        
    Returns:
        Model configuration dictionary
    """
    return _ask_prompts.get_model_config_for_intent(intent)

def get_compliance_disclaimer(risk_level: str = "standard",
                            intent: str = None,
                            includes_options: bool = False) -> str:
    """
    Unified compliance disclaimer function
    
    Args:
        risk_level: Risk level for appropriate disclaimer
        intent: Trading intent for context-specific disclaimer
        includes_options: Whether content includes options trading
        
    Returns:
        Appropriate compliance disclaimer
    """
    if intent:
        return _compliance_manager.get_intent_specific_compliance(intent)
    elif includes_options:
        return _compliance_manager.get_compliance_fragment("options")
    else:
        return _compliance_manager.get_compliance_fragment(risk_level)

def get_persona_prompt_fragment(persona: str = "trading_expert") -> str:
    """
    Unified persona prompt fragment function
    
    Args:
        persona: Persona name
        
    Returns:
        Persona prompt fragment
    """
    return _persona_manager.get_persona_prompt_fragment(persona)

def inject_context(prompt: str,
                  market_data: Optional[Dict[str, Any]] = None,
                  user_context: Optional[Dict[str, Any]] = None,
                  session_context: Optional[Dict[str, Any]] = None) -> str:
    """
    Unified context injection function
    
    Args:
        prompt: Base prompt
        market_data: Market context data
        user_context: User context data
        session_context: Session context data
        
    Returns:
        Prompt with injected context
    """
    return _context_injector.inject_comprehensive_context(
        prompt, market_data, user_context, session_context
    )

def get_intent_detection_prompt(query: str,
                              context: Optional[Dict[str, Any]] = None) -> str:
    """
    Unified intent detection prompt function
    
    Args:
        query: User query to classify
        context: Additional context for classification
        
    Returns:
        Intent detection prompt
    """
    from .services.intent_detection import IntentDetectionPrompts
    intent_prompts = IntentDetectionPrompts()
    
    if context:
        return intent_prompts.get_enhanced_intent_prompt(query, context)
    else:
        return intent_prompts.get_intent_classification_prompt(query)

def get_anti_hallucination_prompt(context: Dict[str, Any]) -> str:
    """
    Unified anti-hallucination prompt function
    
    Args:
        context: Data context for anti-fabrication rules
        
    Returns:
        Anti-hallucination prompt
    """
    from .services.anti_hallucination import AntiHallucinationPrompts
    anti_hal_prompts = AntiHallucinationPrompts()
    return anti_hal_prompts.get_anti_hallucination_prompt(context)

# Backward compatibility aliases
def get_system_prompt_with_date_injection(persona: str = "trading_expert") -> str:
    """Backward compatibility function for date injection"""
    return get_system_prompt(persona=persona, include_json_format=False)

def get_ask_system_prompt(context: Optional[Dict[str, Any]] = None) -> str:
    """Backward compatibility function for ask command"""
    return get_system_prompt(command="ask", context=context)

def get_simple_system_prompt(persona: str = "trading_expert") -> str:
    """Backward compatibility function for simple prompts"""
    return get_system_prompt(persona=persona, include_json_format=False)

# Export all unified functions
__all__ = [
    'get_system_prompt',
    'get_fallback_responses', 
    'get_model_config_for_intent',
    'get_compliance_disclaimer',
    'get_persona_prompt_fragment',
    'inject_context',
    'get_intent_detection_prompt',
    'get_anti_hallucination_prompt',
    # Backward compatibility
    'get_system_prompt_with_date_injection',
    'get_ask_system_prompt',
    'get_simple_system_prompt'
]
