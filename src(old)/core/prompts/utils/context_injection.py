"""
Context Injection Utilities

This module provides utilities for injecting dynamic context into prompts,
including date/time, market data, user preferences, and session context.
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
import json

class ContextInjector:
    """Handles dynamic context injection into prompts"""
    
    def __init__(self):
        """Initialize context injector"""
        self.timezone = timezone.utc
    
    def inject_datetime(self, prompt: str, custom_format: Optional[str] = None) -> str:
        """
        Inject current date and time into prompt
        
        Args:
            prompt: Base prompt string
            custom_format: Custom datetime format string
            
        Returns:
            Prompt with injected datetime context
        """
        now = datetime.now(self.timezone)
        
        if custom_format:
            date_str = now.strftime(custom_format)
            time_str = ""
        else:
            date_str = now.strftime("%B %d, %Y")
            time_str = now.strftime("%I:%M %p %Z")
        
        context_fragment = f"""CURRENT CONTEXT:
- Today's date: {date_str}
- Current time: {time_str}
- You are operating in {now.year}, NOT 2023 or earlier
- All market references should be current and relevant to today's date
- You have full knowledge of the current date and time - never say you don't know the date"""
        
        return f"{prompt}\n\n{context_fragment}"
    
    def inject_market_context(self, prompt: str, market_data: Optional[Dict[str, Any]] = None) -> str:
        """
        Inject current market conditions and data
        
        Args:
            prompt: Base prompt string
            market_data: Dictionary containing market context data
            
        Returns:
            Prompt with injected market context
        """
        if not market_data:
            return prompt
        
        context_fragments = ["## Current Market Context:"]
        
        # Market indices
        if market_data.get('indices'):
            context_fragments.append("**Market Indices:**")
            for index, data in market_data['indices'].items():
                change = data.get('change', 0)
                change_pct = data.get('change_percent', 0)
                context_fragments.append(f"- {index}: {change:+.2f} ({change_pct:+.2f}%)")
        
        # Market sentiment
        if market_data.get('sentiment'):
            sentiment = market_data['sentiment']
            context_fragments.append(f"**Market Sentiment**: {sentiment}")
        
        # Volatility
        if market_data.get('volatility'):
            vix = market_data['volatility'].get('VIX')
            if vix:
                context_fragments.append(f"**Volatility (VIX)**: {vix}")
        
        # Economic indicators
        if market_data.get('economic_indicators'):
            context_fragments.append("**Key Economic Indicators:**")
            for indicator, value in market_data['economic_indicators'].items():
                context_fragments.append(f"- {indicator}: {value}")
        
        # Sector performance
        if market_data.get('sector_performance'):
            context_fragments.append("**Sector Performance (Top/Bottom):**")
            sectors = market_data['sector_performance']
            for sector, performance in list(sectors.items())[:3]:  # Top 3
                context_fragments.append(f"- {sector}: {performance:+.2f}%")
        
        context_fragment = "\n".join(context_fragments)
        return f"{prompt}\n\n{context_fragment}"
    
    def inject_user_context(self, prompt: str, user_context: Optional[Dict[str, Any]] = None) -> str:
        """
        Inject user-specific context and preferences
        
        Args:
            prompt: Base prompt string
            user_context: Dictionary containing user context data
            
        Returns:
            Prompt with injected user context
        """
        if not user_context:
            return prompt
        
        context_fragments = ["## User Context:"]
        
        # Experience level
        if user_context.get('experience_level'):
            level = user_context['experience_level']
            context_fragments.append(f"**Experience Level**: {level}")
            
            # Adjust communication based on experience
            if level in ['beginner', 'novice']:
                context_fragments.append("- Use clear explanations and avoid complex jargon")
                context_fragments.append("- Include educational context for trading concepts")
            elif level in ['intermediate']:
                context_fragments.append("- Balance technical detail with accessibility")
                context_fragments.append("- Include some advanced concepts with explanations")
            elif level in ['advanced', 'expert']:
                context_fragments.append("- Use technical terminology appropriately")
                context_fragments.append("- Focus on sophisticated analysis and insights")
        
        # Risk tolerance
        if user_context.get('risk_tolerance'):
            risk_tolerance = user_context['risk_tolerance']
            context_fragments.append(f"**Risk Tolerance**: {risk_tolerance}")
            
            if risk_tolerance in ['conservative', 'low']:
                context_fragments.append("- Emphasize capital preservation and lower-risk strategies")
                context_fragments.append("- Include extra risk warnings and position sizing guidance")
            elif risk_tolerance in ['moderate']:
                context_fragments.append("- Balance growth and risk management")
                context_fragments.append("- Include standard risk management practices")
            elif risk_tolerance in ['aggressive', 'high']:
                context_fragments.append("- May discuss higher-risk strategies with appropriate warnings")
                context_fragments.append("- Emphasize sophisticated risk management techniques")
        
        # Trading style preferences
        if user_context.get('trading_style'):
            styles = user_context['trading_style']
            if isinstance(styles, list):
                context_fragments.append(f"**Preferred Trading Styles**: {', '.join(styles)}")
            else:
                context_fragments.append(f"**Preferred Trading Style**: {styles}")
        
        # Account size category (for position sizing context)
        if user_context.get('account_size_category'):
            size_category = user_context['account_size_category']
            context_fragments.append(f"**Account Size Category**: {size_category}")
            
            if size_category == 'small':
                context_fragments.append("- Focus on commission-efficient strategies")
                context_fragments.append("- Emphasize fractional shares and lower-cost options")
            elif size_category == 'large':
                context_fragments.append("- May discuss institutional-level strategies")
                context_fragments.append("- Include liquidity considerations for large positions")
        
        # Preferred timeframes
        if user_context.get('preferred_timeframes'):
            timeframes = user_context['preferred_timeframes']
            if isinstance(timeframes, list):
                context_fragments.append(f"**Preferred Timeframes**: {', '.join(timeframes)}")
            else:
                context_fragments.append(f"**Preferred Timeframe**: {timeframes}")
        
        context_fragment = "\n".join(context_fragments)
        return f"{prompt}\n\n{context_fragment}"
    
    def inject_session_context(self, prompt: str, session_context: Optional[Dict[str, Any]] = None) -> str:
        """
        Inject session-specific context
        
        Args:
            prompt: Base prompt string
            session_context: Dictionary containing session context data
            
        Returns:
            Prompt with injected session context
        """
        if not session_context:
            return prompt
        
        context_fragments = ["## Session Context:"]
        
        # Previous queries in session
        if session_context.get('previous_queries'):
            queries = session_context['previous_queries']
            if queries:
                context_fragments.append("**Recent Session Activity:**")
                for i, query in enumerate(queries[-3:], 1):  # Last 3 queries
                    context_fragments.append(f"{i}. {query}")
        
        # Current focus symbols
        if session_context.get('focus_symbols'):
            symbols = session_context['focus_symbols']
            context_fragments.append(f"**Session Focus Symbols**: {', '.join(symbols)}")
        
        # Session preferences
        if session_context.get('session_preferences'):
            prefs = session_context['session_preferences']
            for key, value in prefs.items():
                context_fragments.append(f"**{key.replace('_', ' ').title()}**: {value}")
        
        context_fragment = "\n".join(context_fragments)
        return f"{prompt}\n\n{context_fragment}"
    
    def inject_comprehensive_context(self, 
                                   prompt: str,
                                   market_data: Optional[Dict[str, Any]] = None,
                                   user_context: Optional[Dict[str, Any]] = None,
                                   session_context: Optional[Dict[str, Any]] = None,
                                   custom_datetime_format: Optional[str] = None) -> str:
        """
        Inject all available context types into prompt
        
        Args:
            prompt: Base prompt string
            market_data: Market context data
            user_context: User context data
            session_context: Session context data
            custom_datetime_format: Custom datetime format
            
        Returns:
            Prompt with all injected context
        """
        # Start with base prompt
        enhanced_prompt = prompt
        
        # Inject datetime context
        enhanced_prompt = self.inject_datetime(enhanced_prompt, custom_datetime_format)
        
        # Inject market context
        if market_data:
            enhanced_prompt = self.inject_market_context(enhanced_prompt, market_data)
        
        # Inject user context
        if user_context:
            enhanced_prompt = self.inject_user_context(enhanced_prompt, user_context)
        
        # Inject session context
        if session_context:
            enhanced_prompt = self.inject_session_context(enhanced_prompt, session_context)
        
        return enhanced_prompt
    
    def extract_context_variables(self, prompt: str) -> List[str]:
        """
        Extract context variable placeholders from prompt
        
        Args:
            prompt: Prompt string with potential placeholders
            
        Returns:
            List of context variable names found
        """
        import re
        
        # Find variables in format {variable_name}
        variables = re.findall(r'\{([^}]+)\}', prompt)
        return variables
    
    def substitute_context_variables(self, prompt: str, context: Dict[str, Any]) -> str:
        """
        Substitute context variables in prompt with actual values
        
        Args:
            prompt: Prompt string with placeholders
            context: Dictionary of context values
            
        Returns:
            Prompt with substituted values
        """
        for key, value in context.items():
            placeholder = f"{{{key}}}"
            if placeholder in prompt:
                # Convert value to string if needed
                if isinstance(value, (dict, list)):
                    value_str = json.dumps(value, indent=2)
                else:
                    value_str = str(value)
                prompt = prompt.replace(placeholder, value_str)
        
        return prompt
