"""
System Prompt Management for Trading Bot

This module provides centralized system prompts that combine personas
with core system instructions, anti-fabrication rules, and context injection.
"""

from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path

from .personas import PersonaManager, PERSONAS
from .compliance import ComplianceManager

class SystemPromptManager:
    """Manages system prompts with persona integration and context injection"""
    
    def __init__(self, templates_dir: Optional[Path] = None):
        """Initialize system prompt manager"""
        self.templates_dir = templates_dir or Path(__file__).parent.parent / "templates"
        self.persona_manager = PersonaManager()
        self.compliance_manager = ComplianceManager()
        
        # Core system prompt components
        self.base_system_prompt = self._load_base_system_prompt()
        self.anti_fabrication_rules = self._get_anti_fabrication_rules()
        self.data_strategy_rules = self._get_data_strategy_rules()
        self.response_format_rules = self._get_response_format_rules()
    
    def _load_base_system_prompt(self) -> str:
        """Load base system prompt from template file"""
        prompt_file = self.templates_dir / "system_prompt.txt"
        if prompt_file.exists():
            return prompt_file.read_text()
        return self._get_default_base_prompt()
    
    def _get_default_base_prompt(self) -> str:
        """Get default base system prompt"""
        return """You are a professional trading analysis assistant. Your role is to analyze user queries, extract relevant information, and provide educational trading insights with proper risk disclosures.

## Core Responsibilities:
1. **Intent Classification**: Accurately categorize user queries into specific trading contexts
2. **Symbol Extraction**: Identify and clean stock symbols from user input
3. **Data Requirements**: Determine when real-time market data is essential vs. educational content
4. **Complete Analysis**: Provide comprehensive, actionable trading education and market insights

## Intent Categories:
- `price_check`: Current price requests, quotes, basic market data
- `technical_analysis`: Chart patterns, indicators, support/resistance analysis, comprehensive technical data requests
- `fundamental_analysis`: Financial metrics, earnings, valuation analysis
- `options_strategy`: Option plays, strategies, volatility analysis
- `market_overview`: Sector analysis, broad market conditions
- `risk_management`: Position sizing, stop losses, portfolio protection
- `educational`: Trading concepts, strategy explanations, learning content"""
    
    def _get_anti_fabrication_rules(self) -> str:
        """Get anti-fabrication and data integrity rules"""
        return """## CRITICAL: DATA INTEGRITY & ANTI-FABRICATION (ABSOLUTELY NO EXCEPTIONS)
- NEVER EVER fabricate stock prices, market data, earnings dates, or economic events
- NEVER create fake trading scenarios with made-up prices and events
- NEVER fabricate current market conditions, stock performance, or market sentiment
- If you don't have real-time data, say "I need current market data to provide accurate analysis"
- DO NOT create elaborate fake market scenarios - users need real information
- You MAY propose trade structures (options or equity) with expiration windows and strike ranges when asked
- Prefer concrete suggestions over generic education when the user asks for plays

### Price Validation Rules:
1. **ALL price levels must be within 20% of the current price provided**
2. **Support levels**: 90-95% of current price
3. **Resistance levels**: 105-110% of current price  
4. **Entry levels**: 95-105% of current price
5. **NEVER mention price levels that are 3x+ different from current price**"""
    
    def _get_data_strategy_rules(self) -> str:
        """Get data strategy and tool usage rules"""
        return """## Data Strategy (CRITICAL):
**AUTOMATIC DATA FETCHING**: Always automatically fetch required data without asking for permission
- Use `tools_required` array to specify which data tools are needed for accurate analysis
- Available tools: `["price_fetch", "historical_data", "technical_indicators", "fundamental_data", "options_data"]`
- NEVER ask for permission to pull data - just do it automatically
- Always provide complete analysis with real data when symbols are mentioned
- Focus on delivering actionable insights with current market data"""
    
    def _get_response_format_rules(self) -> str:
        """Get response format and structure rules"""
        return """## Response Requirements:
**Format**: Valid JSON with exact structure specified below
**Length**: 200-400 words maximum (Discord-optimized)
**Tone**: Professional, educational, risk-aware
**Compliance**: Include appropriate disclaimers and risk warnings

## Required JSON Structure:
```json
{
  "intent": "string (one of the 7 categories above)",
  "symbols": ["SYMBOL1", "SYMBOL2"],
  "tools_required": ["price_fetch", "historical_data"],
  "confidence": float (0.0-1.0),
  "timeframe": "string (intraday|short_term|medium_term|long_term)",
  "risk_level": "string (low|medium|high|very_high)",
  "response": "Complete educational response with disclaimers"
}
```"""
    
    def get_system_prompt(self, 
                         persona: str = "trading_expert",
                         context: Optional[Dict[str, Any]] = None,
                         include_json_format: bool = True,
                         include_anti_fabrication: bool = True) -> str:
        """
        Generate a complete system prompt with persona and context
        
        Args:
            persona: Persona name to use
            context: Additional context to inject
            include_json_format: Whether to include JSON format requirements
            include_anti_fabrication: Whether to include anti-fabrication rules
            
        Returns:
            Complete system prompt string
        """
        # Get current date/time context
        current_date = datetime.now().strftime("%B %d, %Y")
        current_time = datetime.now().strftime("%I:%M %p %Z")
        
        # Build prompt components
        components = []
        
        # JSON format requirement (if needed)
        if include_json_format:
            components.append("""CRITICAL FORMATTING RULE: Respond EXCLUSIVELY with the valid JSON object as specified below. Do NOT include any additional text, explanations, markdown, or prose before or after the JSON. The JSON must be the ONLY content in your response. Invalid formats will cause processing errors.

Example of REQUIRED response format (use this exact structure):
{
  "intent": "technical_analysis",
  "symbols": ["AAPL", "MSFT"],
  "tools_required": ["price_fetch", "historical_data", "technical_indicators"],
  "confidence": 0.95,
  "timeframe": "short_term",
  "risk_level": "medium",
  "response": "Your complete analysis here with disclaimers..."
}""")
        
        # Persona integration
        persona_fragment = self.persona_manager.get_persona_prompt_fragment(persona)
        components.append(persona_fragment)
        
        # Current context
        components.append(f"""CURRENT CONTEXT:
- Today's date: {current_date}
- Current time: {current_time}
- You are operating in 2025, NOT 2023 or earlier
- All market references should be current and relevant to today's date
- You have full knowledge of the current date and time - never say you don't know the date""")
        
        # Anti-fabrication rules
        if include_anti_fabrication:
            components.append(self.anti_fabrication_rules)
        
        # Base system prompt
        components.append(self.base_system_prompt)
        
        # Data strategy rules
        components.append(self.data_strategy_rules)
        
        # Response format rules
        if include_json_format:
            components.append(self.response_format_rules)
        
        # Compliance requirements
        disclaimer_level = self.persona_manager.get_disclaimer_level(persona)
        compliance_fragment = self.compliance_manager.get_compliance_fragment(disclaimer_level)
        components.append(compliance_fragment)
        
        # Additional context injection
        if context:
            context_fragment = self._build_context_fragment(context)
            if context_fragment:
                components.append(context_fragment)
        
        return "\n\n".join(components)
    
    def _build_context_fragment(self, context: Dict[str, Any]) -> str:
        """Build context fragment from provided context data"""
        fragments = []
        
        if context.get('market_data'):
            fragments.append("## Current Market Context:")
            market_data = context['market_data']
            for key, value in market_data.items():
                fragments.append(f"- {key}: {value}")
        
        if context.get('user_preferences'):
            fragments.append("## User Preferences:")
            prefs = context['user_preferences']
            for key, value in prefs.items():
                fragments.append(f"- {key}: {value}")
        
        if context.get('session_context'):
            fragments.append("## Session Context:")
            session = context['session_context']
            for key, value in session.items():
                fragments.append(f"- {key}: {value}")
        
        return "\n".join(fragments) if fragments else ""
    
    def get_simple_system_prompt(self, persona: str = "trading_expert") -> str:
        """Get a simple system prompt without JSON formatting requirements"""
        return self.get_system_prompt(
            persona=persona,
            include_json_format=False,
            include_anti_fabrication=True
        )
    
    def get_command_system_prompt(self, command: str, persona: str = None) -> str:
        """Get system prompt optimized for a specific command"""
        # Auto-select persona based on command if not specified
        if not persona:
            command_persona_mapping = {
                "ask": "trading_expert",
                "analyze": "market_analyst", 
                "risk": "risk_analyst",
                "options": "options_specialist",
                "learn": "educational_assistant"
            }
            persona = command_persona_mapping.get(command, "trading_expert")
        
        return self.get_system_prompt(persona=persona)
