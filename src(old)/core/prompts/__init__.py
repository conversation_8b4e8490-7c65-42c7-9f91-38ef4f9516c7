"""
Core prompt management module for trading analysis system.
Located at: src/core/prompts/

This module centralizes all prompt-related functionality including:
- Intent classification
- Symbol extraction
- Tool requirement determination
- Response generation
- Compliance validation
- AI personas and personalities
- System and command prompts
- Context injection and formatting
"""

from .models import (
    IntentType, ToolType, IntentClassification, AnalysisPrompt,
    ComplianceTemplate, PromptResult
)

from .prompt_manager import PromptManager

# Import new centralized components (with fallback for migration)
try:
    from .base.personas import PersonaManager, PERSONAS
    from .base.system_prompts import SystemPromptManager
    from .base.compliance import ComplianceManager
    from .commands.ask_prompts import AskCommandPrompts
    from .services.intent_detection import IntentDetectionPrompts
    from .utils.context_injection import ContextInjector
    from .utils.validation import PromptValidator
    from .utils.formatters import PromptFormatter
    CENTRALIZED_AVAILABLE = True
except ImportError:
    # Fallback during migration - components will be created as needed
    CENTRALIZED_AVAILABLE = False

__all__ = [
    # Existing models
    'IntentType',
    'ToolType',
    'IntentClassification',
    'AnalysisPrompt',
    'ComplianceTemplate',
    'PromptResult',
    'PromptManager',

    # Global instances
    'prompt_manager',

    # Unified prompt functions (primary interface)
    'get_system_prompt',
    'get_fallback_responses',
    'get_model_config_for_intent',
    'get_compliance_disclaimer',
    'get_persona_prompt_fragment',
    'inject_context',
    'get_intent_detection_prompt',
    'get_anti_hallucination_prompt'
]

# Add centralized components to exports if available
if CENTRALIZED_AVAILABLE:
    __all__.extend([
        'PersonaManager',
        'SystemPromptManager',
        'ComplianceManager',
        'AskCommandPrompts',
        'IntentDetectionPrompts',
        'ContextInjector',
        'PromptValidator',
        'PromptFormatter',
        'PERSONAS',
        'persona_manager',
        'system_prompt_manager',
        'compliance_manager',
        'context_injector'
    ])

# Initialize global instances
prompt_manager = PromptManager()

# Initialize centralized components if available
if CENTRALIZED_AVAILABLE:
    persona_manager = PersonaManager()
    system_prompt_manager = SystemPromptManager()
    compliance_manager = ComplianceManager()
    context_injector = ContextInjector()

    # Import and expose unified prompt functions
    from .unified_prompts import (
        get_system_prompt,
        get_fallback_responses,
        get_model_config_for_intent,
        get_compliance_disclaimer,
        get_persona_prompt_fragment,
        inject_context,
        get_intent_detection_prompt,
        get_anti_hallucination_prompt
    )
else:
    # Placeholder instances for backward compatibility
    persona_manager = None
    system_prompt_manager = None
    compliance_manager = None
    context_injector = None

    # Fallback functions for backward compatibility
    def get_system_prompt(*args, **kwargs):
        return prompt_manager.get_enhanced_system_prompt(*args, **kwargs)

    def get_fallback_responses(*args, **kwargs):
        return prompt_manager.get_fallback_responses(*args, **kwargs)

    def get_model_config_for_intent(*args, **kwargs):
        return {"temperature": 0.3, "max_tokens": 1000}

    def get_compliance_disclaimer(*args, **kwargs):
        return "⚠️ This is educational content, not financial advice."

    def get_persona_prompt_fragment(*args, **kwargs):
        return "You are a professional trading assistant."

    def inject_context(prompt, *args, **kwargs):
        return prompt_manager.inject_context(prompt, *args, **kwargs)

    def get_intent_detection_prompt(*args, **kwargs):
        return prompt_manager.get_intent_detection_prompt(*args, **kwargs)

    def get_anti_hallucination_prompt(*args, **kwargs):
        return "Never fabricate market data or financial information."