"""
Analysis Template for Strict Technical Analysis
Provides structured, consistent formatting for analysis responses
"""

from typing import Optional, List, Dict, Any
from dataclasses import dataclass
from datetime import datetime
import re

@dataclass
class AnalysisTemplate:
    """Structured template for technical analysis responses
    
    Handles data from multiple providers with different field names.
    Automatically normalizes common field names.
    """
    
    symbol: str
    current_price: Optional[float] = None
    price: Optional[float] = None  # Alias for current_price
    target_price: Optional[str] = None
    indicators: Optional[str] = None
    sentiment: Optional[str] = None
    analysis_text: str = ""
    risk_reward: Optional[float] = None
    entry_point: Optional[float] = None
    stop_loss: Optional[float] = None
    source: Optional[str] = None
    
    def __post_init__(self):
        """Normalize field names after initialization"""
        # Ensure we have a current_price, even if it was provided as 'price'
        if self.current_price is None and self.price is not None:
            self.current_price = self.price
        elif self.price is None and self.current_price is not None:
            self.price = self.current_price
    
    def generate(self) -> str:
        """Generate a structured analysis response"""
        
        # Ensure we have a valid price to display
        display_price = self.current_price or self.price
        
        # Header
        header = f"🎯 **{self.symbol.upper()} Technical Analysis**\n"
        if self.source:
            header += f"📊 Source: {self.source.upper()}\n"
        header += f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        if display_price is not None:
            header += f"💰 Current Price: ${display_price:,.2f}\n"
        header += "\n"
        
        # Price Information
        price_section = ""
        if self.current_price:
            price_section += f"💰 **Current Price**: ${self.current_price:.2f}\n"
        
        if self.target_price:
            price_section += f"🎯 **Target Price**: {self.target_price}\n"
            
            # Calculate risk/reward if we have both prices
            if self.current_price and self.target_price:
                target_float = self._extract_price_from_string(self.target_price)
                if target_float:
                    risk_reward = abs((target_float - self.current_price) / self.current_price)
                    price_section += f"📊 **Risk/Reward**: {risk_reward:.2f}x\n"
        
        if self.entry_point:
            price_section += f"🚀 **Entry Point**: ${self.entry_point:.2f}\n"
            
        if self.stop_loss:
            price_section += f"🛑 **Stop Loss**: ${self.stop_loss:.2f}\n"
        
        if price_section:
            price_section += "\n"
        
        # Technical Indicators
        indicators_section = ""
        if self.indicators:
            indicators_section += f"📈 **Technical Indicators**:\n"
            # Parse and format indicators
            formatted_indicators = self._format_indicators(self.indicators)
            indicators_section += formatted_indicators + "\n\n"
        
        # Sentiment
        sentiment_section = ""
        if self.sentiment:
            sentiment_emoji = self._get_sentiment_emoji(self.sentiment)
            sentiment_section += f"{sentiment_emoji} **Sentiment**: {self.sentiment.title()}\n\n"
        
        # Analysis Text
        analysis_section = ""
        if self.analysis_text:
            # Clean up the analysis text
            cleaned_text = self._clean_analysis_text(self.analysis_text)
            analysis_section += f"📝 **Analysis**:\n{cleaned_text}\n\n"
        
        # Risk Disclaimer
        disclaimer = (
            "⚠️ **Risk Disclaimer**:\n"
            "• This analysis is for informational purposes only\n"
            "• Past performance does not guarantee future results\n"
            "• Always conduct your own research before investing\n"
            "• Consider consulting a financial advisor\n\n"
        )
        
        # Combine all sections
        full_response = header + price_section + indicators_section + sentiment_section + analysis_section + disclaimer
        
        return full_response.strip()
    
    def _extract_price_from_string(self, price_str: str) -> Optional[float]:
        """Extract numeric price from string like '$150.50'"""
        try:
            # Remove $ and any non-numeric characters except decimal
            clean_price = re.sub(r'[^\d.]', '', price_str)
            return float(clean_price)
        except (ValueError, AttributeError):
            return None
    
    def _format_indicators(self, indicators_str: str) -> str:
        """Format indicators string for better readability"""
        if not indicators_str:
            return "None specified"
        
        # Split by common separators
        indicators = re.split(r'[,;]', indicators_str)
        
        formatted = []
        for indicator in indicators:
            indicator = indicator.strip()
            if indicator:
                # Add bullet point and format
                formatted.append(f"• {indicator}")
        
        return "\n".join(formatted) if formatted else "None specified"
    
    def _get_sentiment_emoji(self, sentiment: str) -> str:
        """Get appropriate emoji for sentiment"""
        sentiment_lower = sentiment.lower()
        
        if sentiment_lower in ['bullish', 'bull', 'positive', 'up']:
            return "🚀"
        elif sentiment_lower in ['bearish', 'bear', 'negative', 'down']:
            return "📉"
        else:
            return "➡️"
    
    def _clean_analysis_text(self, text: str) -> str:
        """Clean and format the analysis text"""
        if not text:
            return "No analysis provided"
        
        # Remove excessive whitespace
        cleaned = re.sub(r'\s+', ' ', text.strip())
        
        # Ensure proper sentence endings
        if not cleaned.endswith(('.', '!', '?')):
            cleaned += '.'
        
        # Limit length to reasonable size
        if len(cleaned) > 500:
            cleaned = cleaned[:500] + "..."
        
        return cleaned

@dataclass
class PortfolioTemplate:
    """Template for portfolio analysis responses"""
    
    symbols: List[str]
    total_value: Optional[float] = None
    allocation: Dict[str, float] = None
    performance: Dict[str, float] = None
    recommendations: List[str] = None
    
    def generate(self) -> str:
        """Generate portfolio analysis response"""
        
        header = f"📊 **Portfolio Analysis**\n"
        header += f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # Portfolio Overview
        overview = f"🔍 **Portfolio Overview**:\n"
        overview += f"• **Symbols**: {', '.join(self.symbols)}\n"
        if self.total_value:
            overview += f"• **Total Value**: ${self.total_value:,.2f}\n"
        overview += "\n"
        
        # Allocation
        allocation_section = ""
        if self.allocation:
            allocation_section += "📈 **Allocation**:\n"
            for symbol, percentage in self.allocation.items():
                allocation_section += f"• {symbol}: {percentage:.1f}%\n"
            allocation_section += "\n"
        
        # Performance
        performance_section = ""
        if self.performance:
            performance_section += "📊 **Performance**:\n"
            for symbol, change in self.performance.items():
                emoji = "🟢" if change >= 0 else "🔴"
                performance_section += f"• {symbol}: {emoji} {change:+.2f}%\n"
            performance_section += "\n"
        
        # Recommendations
        recommendations_section = ""
        if self.recommendations:
            recommendations_section += "💡 **Recommendations**:\n"
            for rec in self.recommendations:
                recommendations_section += f"• {rec}\n"
            recommendations_section += "\n"
        
        # Disclaimer
        disclaimer = (
            "⚠️ **Risk Disclaimer**:\n"
            "• Portfolio analysis is for informational purposes only\n"
            "• Past performance does not guarantee future results\n"
            "• Always conduct your own research before investing\n\n"
        )
        
        # Combine all sections
        full_response = header + overview + allocation_section + performance_section + recommendations_section + disclaimer
        
        return full_response.strip()

@dataclass
class MarketTrendTemplate:
    """Template for market trend analysis"""
    
    trend: str
    sectors: List[str] = None
    key_drivers: List[str] = None
    outlook: str = ""
    timeframe: str = "short-term"
    
    def generate(self) -> str:
        """Generate market trend analysis response"""
        
        header = f"🌍 **Market Trend Analysis**\n"
        header += f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # Trend Summary
        trend_emoji = self._get_trend_emoji(self.trend)
        trend_section = f"{trend_emoji} **Overall Trend**: {self.trend.title()}\n"
        trend_section += f"⏰ **Timeframe**: {self.timeframe.title()}\n\n"
        
        # Sectors
        sectors_section = ""
        if self.sectors:
            sectors_section += "🏢 **Key Sectors**:\n"
            for sector in self.sectors:
                sectors_section += f"• {sector}\n"
            sectors_section += "\n"
        
        # Key Drivers
        drivers_section = ""
        if self.key_drivers:
            drivers_section += "🔑 **Key Drivers**:\n"
            for driver in self.key_drivers:
                drivers_section += f"• {driver}\n"
            drivers_section += "\n"
        
        # Outlook
        outlook_section = ""
        if self.outlook:
            outlook_section += f"🔮 **Outlook**:\n{self.outlook}\n\n"
        
        # Disclaimer
        disclaimer = (
            "⚠️ **Risk Disclaimer**:\n"
            "• Market analysis is for informational purposes only\n"
            "• Market conditions can change rapidly\n"
            "• Always conduct your own research before investing\n\n"
        )
        
        # Combine all sections
        full_response = header + trend_section + sectors_section + drivers_section + outlook_section + disclaimer
        
        return full_response.strip()
    
    def _get_trend_emoji(self, trend: str) -> str:
        """Get appropriate emoji for trend"""
        trend_lower = trend.lower()
        
        if trend_lower in ['bullish', 'bull', 'positive', 'up', 'rising']:
            return "🚀"
        elif trend_lower in ['bearish', 'bear', 'negative', 'down', 'falling']:
            return "📉"
        elif trend_lower in ['sideways', 'neutral', 'consolidating']:
            return "➡️"
        else:
            return "📊" 