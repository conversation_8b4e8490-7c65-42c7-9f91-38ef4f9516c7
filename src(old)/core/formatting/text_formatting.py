from typing import Union, Optional
from datetime import datetime

def format_currency(amount: Union[int, float], currency: str = '$') -> str:
    """
    Format a number as currency with 2 decimal places

    Args:
        amount (Union[int, float]): The monetary amount
        currency (str, optional): Currency symbol. Defaults to '$'

    Returns:
        str: Formatted currency string
    """
    try:
        # Always format to 2 decimal places
        return f"{currency}{amount:.2f}"
    except (ValueError, TypeError):
        return f"{currency}0.00"

def format_price(amount: Union[int, float]) -> str:
    """
    Format a price with smart precision (alias for format_currency)

    Args:
        amount (Union[int, float]): The price amount

    Returns:
        str: Formatted price string
    """
    return format_currency(amount)

def format_percentage(value: Union[int, float], decimal_places: int = 2) -> str:
    """
    Format a number as a percentage
    
    Args:
        value (Union[int, float]): The percentage value
        decimal_places (int, optional): Number of decimal places. Defaults to 2
    
    Returns:
        str: Formatted percentage string
    """
    try:
        return f"{value:.{decimal_places}f}%"
    except (ValueError, TypeError):
        return "0.00%"

def format_timestamp(
    timestamp: Optional[datetime] = None, 
    format_string: str = "%Y-%m-%d %H:%M:%S"
) -> str:
    """
    Format a timestamp
    
    Args:
        timestamp (Optional[datetime], optional): Timestamp to format. Defaults to current time.
        format_string (str, optional): Formatting string. Defaults to "%Y-%m-%d %H:%M:%S"
    
    Returns:
        str: Formatted timestamp string
    """
    if timestamp is None:
        timestamp = datetime.now()
    
    return timestamp.strftime(format_string)

def truncate_text(
    text: str, 
    max_length: int = 200, 
    ellipsis: str = "..."
) -> str:
    """
    Truncate text to a specified length
    
    Args:
        text (str): Text to truncate
        max_length (int, optional): Maximum length. Defaults to 200.
        ellipsis (str, optional): Ellipsis string. Defaults to "..."
    
    Returns:
        str: Truncated text
    """
    if len(text) <= max_length:
        return text
    return text[:max_length].rsplit(' ', 1)[0] + ellipsis

def humanize_large_number(number: Union[int, float]) -> str:
    """
    Convert large numbers to human-readable format
    
    Args:
        number (Union[int, float]): Number to convert
    
    Returns:
        str: Humanized number string
    """
    try:
        number = float(number)
        if number >= 1_000_000_000:
            return f"{number/1_000_000_000:.1f}B"
        elif number >= 1_000_000:
            return f"{number/1_000_000:.1f}M"
        elif number >= 1_000:
            return f"{number/1_000:.1f}K"
        return f"{number:.2f}"
    except (ValueError, TypeError):
        return "0"

def generate_data_quality_indicator(
    data_age: Optional[datetime] = None, 
    max_age_days: int = 30
) -> str:
    """
    Generate a data quality indicator based on data age
    
    Args:
        data_age (Optional[datetime], optional): Age of the data. Defaults to None.
        max_age_days (int, optional): Maximum acceptable age. Defaults to 30.
    
    Returns:
        str: Data quality indicator emoji
    """
    if data_age is None:
        return "❓"  # Unknown
    
    age = datetime.now() - data_age
    
    if age.days <= max_age_days / 3:
        return "🟢"  # Fresh data
    elif age.days <= max_age_days:
        return "🟡"  # Aging data
    else:
        return "🔴"  # Stale data 