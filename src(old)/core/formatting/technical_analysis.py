"""
Technical Analysis Formatter

Handles formatting and validation of technical indicator data,
separating this logic from the chat processor for better maintainability.
"""

from typing import Dict, Any, List, Optional, Union, Set, Tuple, TypedDict, cast


class ZoneMetrics(TypedDict, total=False):
    """Type definition for zone metrics data"""
    total_zones: int
    supply_zone_count: int
    demand_zone_count: int
    nearest_supply: Dict[str, Any]
    nearest_demand: Dict[str, Any]


class Zone(TypedDict, total=False):
    """Type definition for supply/demand zone data"""
    zone_type: str
    center_price: float
    bottom_price: float
    top_price: float
    strength: int
    touches: int


class SymbolData(TypedDict, total=False):
    """Type definition for symbol data"""
    technical_indicators_available: bool
    current_price: float
    historical: List[Dict[str, Any]]
    rsi: float
    macd: float
    macd_signal: float
    macd_histogram: float
    sma_20: float
    sma_50: float
    ema_12: float
    ema_26: float
    support_levels: List[float]
    resistance_levels: List[float]
    bollinger_upper: float
    bollinger_middle: float
    bollinger_lower: float
    volume_sma: float
    supply_demand_zones: List[Zone]
    zone_metrics: ZoneMetrics


class TechnicalAnalysisFormatter:
    """Handles formatting and validation of technical indicator data."""
    
    @staticmethod
    def has_technical_data(symbol_data: Union[Dict[str, Any], SymbolData]) -> bool:
        """
        Check if technical indicators are present in the data.
        
        Args:
            symbol_data: Dictionary containing symbol data with potential indicators
            
        Returns:
            True if any technical indicators are available, False otherwise
        """
        # Check for the flag first
        if symbol_data.get('technical_indicators_available'):
            return True
        
        # Check for specific indicator values
        technical_indicators = [
            'rsi', 'macd', 'macd_signal', 'macd_histogram',
            'sma_20', 'sma_50', 'ema_12', 'ema_26',
            'support_levels', 'resistance_levels',
            'bollinger_upper', 'bollinger_middle', 'bollinger_lower',
            'volume_sma', 'supply_demand_zones'
        ]
        
        return any(
            symbol_data.get(indicator) is not None 
            for indicator in technical_indicators
        )
    
    @staticmethod
    def has_price_data(symbol_data: Union[Dict[str, Any], SymbolData]) -> bool:
        """
        Check if price data is available.
        
        Args:
            symbol_data: Dictionary containing symbol data
            
        Returns:
            True if price data is available, False otherwise
        """
        return symbol_data.get('current_price') is not None
    
    @staticmethod
    def has_historical_data(symbol_data: Union[Dict[str, Any], SymbolData]) -> bool:
        """
        Check if historical data is available.
        
        Args:
            symbol_data: Dictionary containing symbol data
            
        Returns:
            True if historical data is available, False otherwise
        """
        return bool(symbol_data.get('historical'))
    
    @staticmethod
    def format_indicators_summary(symbol_data: Union[Dict[str, Any], SymbolData]) -> str:
        """
        Convert raw indicators into a user-friendly summary.
        
        Args:
            symbol_data: Dictionary containing symbol data with indicators
            
        Returns:
            Formatted string with available indicators
        """
        indicators = []
        
        # RSI
        if symbol_data.get('rsi') is not None:
            indicators.append(f"RSI: {symbol_data['rsi']}")
        
        # MACD
        if symbol_data.get('macd') is not None:
            macd_text = f"MACD: {symbol_data['macd']}"
            if symbol_data.get('macd_signal') is not None:
                macd_text += f" (Signal: {symbol_data['macd_signal']})"
            indicators.append(macd_text)
        
        # Moving Averages
        if symbol_data.get('sma_20') is not None:
            indicators.append(f"20-Day SMA: {symbol_data['sma_20']}")
        if symbol_data.get('sma_50') is not None:
            indicators.append(f"50-Day SMA: {symbol_data['sma_50']}")
        if symbol_data.get('ema_12') is not None:
            indicators.append(f"12-Day EMA: {symbol_data['ema_12']}")
        if symbol_data.get('ema_26') is not None:
            indicators.append(f"26-Day EMA: {symbol_data['ema_26']}")
        
        # Support/Resistance
        if symbol_data.get('support_levels'):
            support = symbol_data['support_levels']
            if isinstance(support, list) and support:
                indicators.append(f"Support: {support[0]}")
        
        # Resistance breakout detection with null safety
        current_price = symbol_data.get('current_price')
        if symbol_data.get('resistance_levels') and symbol_data['resistance_levels'][0] is not None and current_price is not None:
            resistance = symbol_data['resistance_levels'][0]
            if current_price > resistance:
                indicators.append(f"Resistance (broken): {resistance}")
            else:
                indicators.append(f"Resistance: {resistance}")
        
        # Bollinger Bands
        if symbol_data.get('bollinger_upper') is not None:
            indicators.append(f"Bollinger Upper: {symbol_data['bollinger_upper']}")
        if symbol_data.get('bollinger_lower') is not None:
            indicators.append(f"Bollinger Lower: {symbol_data['bollinger_lower']}")
        
        # Volume
        if symbol_data.get('volume_sma') is not None:
            indicators.append(f"Volume SMA: {symbol_data['volume_sma']}")
        
        if not indicators:
            return "No technical indicators available"
        
        return "\n".join(f"• {indicator}" for indicator in indicators)
    
    @staticmethod
    def format_zone_analysis(symbol_data: Union[Dict[str, Any], SymbolData]) -> str:
        """
        Format supply and demand zone analysis for display.
        
        Args:
            symbol_data: Dictionary containing symbol data with zone information
            
        Returns:
            Formatted string with zone analysis
        """
        zones: List[Zone] = symbol_data.get('supply_demand_zones', [])
        zone_metrics: Dict[str, Any] = symbol_data.get('zone_metrics', {})
        
        if not zones:
            return "No supply or demand zones detected."
        
        # Sort zones by strength
        zones_sorted: List[Zone] = sorted(zones, key=lambda x: x.get('strength', 0), reverse=True)
        
        # Group by zone type
        supply_zones: List[Zone] = [z for z in zones_sorted if z.get('zone_type') == 'supply']
        demand_zones: List[Zone] = [z for z in zones_sorted if z.get('zone_type') == 'demand']
        
        current_price: float = symbol_data.get('current_price', 0)
        
        lines: List[str] = ["**Supply & Demand Zone Analysis**"]
        
        # Current price context
        if current_price > 0:
            lines.append(f"\n**Current Price: ${current_price:.2f}**")
        
        # Supply zones
        if supply_zones:
            lines.append(f"\n**📈 Supply Zones (Resistance)**")
            for i, zone in enumerate(supply_zones[:3], 1):
                center = zone.get('center_price', 0)
                bottom = zone.get('bottom_price', 0)
                top = zone.get('top_price', 0)
                strength = zone.get('strength', 0)
                touches = zone.get('touches', 0)
                
                # Calculate distance from current price
                distance_pct = ((bottom - current_price) / current_price * 100) if current_price > 0 else 0
                
                lines.append(f"{i}. **${bottom:.2f} - ${top:.2f}** (Center: ${center:.2f})")
                lines.append(f"   Strength: {strength}/100 | Touches: {touches}")
                if distance_pct > 0:
                    lines.append(f"   Distance: {distance_pct:+.1f}%")
        
        # Demand zones
        if demand_zones:
            lines.append(f"\n**📉 Demand Zones (Support)**")
            for i, zone in enumerate(demand_zones[:3], 1):
                center = zone.get('center_price', 0)
                bottom = zone.get('bottom_price', 0)
                top = zone.get('top_price', 0)
                strength = zone.get('strength', 0)
                touches = zone.get('touches', 0)
                
                # Calculate distance from current price
                distance_pct = ((top - current_price) / current_price * 100) if current_price > 0 else 0
                
                lines.append(f"{i}. **${bottom:.2f} - ${top:.2f}** (Center: ${center:.2f})")
                lines.append(f"   Strength: {strength}/100 | Touches: {touches}")
                if distance_pct < 0:
                    lines.append(f"   Distance: {abs(distance_pct):+.1f}%")
        
        # Zone statistics
        if zone_metrics:
            lines.append(f"\n**Zone Statistics**")
            lines.append(f"Total Zones: {zone_metrics.get('total_zones', 0)}")
            lines.append(f"Supply Zones: {zone_metrics.get('supply_zone_count', 0)}")
            lines.append(f"Demand Zones: {zone_metrics.get('demand_zone_count', 0)}")
            if zone_metrics.get('nearest_supply'):
                lines.append(f"Nearest Supply: ${zone_metrics['nearest_supply'].get('center_price', 0):.2f}")
            if zone_metrics.get('nearest_demand'):
                lines.append(f"Nearest Demand: ${zone_metrics['nearest_demand'].get('center_price', 0):.2f}")
        
        return "\n".join(lines)
    
    @staticmethod
    def get_data_availability_summary(data: Dict[str, Union[Dict[str, Any], SymbolData]]) -> Dict[str, bool]:
        """
        Get a summary of what types of data are available across all symbols.
        
        Args:
            data: Dictionary mapping symbols to their data
            
        Returns:
            Dictionary with boolean flags for each data type
        """
        has_price: bool = False
        has_technical: bool = False
        has_historical: bool = False
        
        for symbol_data in data.values():
            if TechnicalAnalysisFormatter.has_price_data(symbol_data):
                has_price = True
            if TechnicalAnalysisFormatter.has_technical_data(symbol_data):
                has_technical = True
            if TechnicalAnalysisFormatter.has_historical_data(symbol_data):
                has_historical = True
            
            # Early exit if we have all types
            if has_price and has_technical and has_historical:
                break
        
        return {
            'has_price_data': has_price,
            'has_technical_data': has_technical,
            'has_historical_data': has_historical
        }
    
    @staticmethod
    def format_market_status_notice(market_context: str, data: Dict[str, Union[Dict[str, Any], SymbolData]]) -> str:
        """
        Generate a helpful market status notice based on available data.
        
        Args:
            market_context: Current market context string
            data: Dictionary mapping symbols to their data
            
        Returns:
            Formatted market status notice
        """
        availability: Dict[str, bool] = TechnicalAnalysisFormatter.get_data_availability_summary(data)
        
        if "CLOSED" in market_context:
            # Markets are closed - explain what data is available
            available_services: List[str] = []
            if availability['has_price_data']:
                available_services.append("✅ **Current prices** (may be delayed from market close)")
            if availability['has_technical_data']:
                available_services.append("✅ **Technical indicators** (RSI, MACD, moving averages)")
            if availability['has_historical_data']:
                available_services.append("✅ **Historical data** (last 7 days of trading)")
            
            if available_services:
                services_text = "\n".join(available_services)
                return f"**📊 Market Status: Closed**\n\n{market_context}\n\n**Available Data:**\n{services_text}\n\n**Note:** While markets are closed, I can still provide:\n• Price information (may be delayed)\n• Technical analysis and indicators\n• Historical performance data\n• Educational content and explanations\n\n**For real-time trading:** Please wait until markets reopen during trading hours (9:30 AM - 4:00 PM ET)."
            else:
                return f"**📊 Market Status: Closed**\n\n{market_context}\n\n**Data Availability:** Limited data available during closed hours.\n\n**What I can help with:**\n• Educational content about trading and analysis\n• Explanation of technical indicators\n• General market knowledge and strategies\n• Company information and fundamentals\n\n**For current market data:** Please try again during trading hours (9:30 AM - 4:00 PM ET)."
                
        elif "Closing soon" in market_context:
            # Markets are closing soon - provide helpful context
            return f"**⚠️ Market Closing Soon**\n\n{market_context}\n\n**Current Status:** Markets are approaching closing time. Data may become limited.\n\n**What I can still provide:**\n• Current prices and market data\n• Technical indicators and analysis\n• Historical performance data\n• Educational content and explanations\n\n**Recommendation:** For the most current data, consider your queries during active trading hours."
        else:
            return ""  # No specific notice needed if markets are open or context is neutral 