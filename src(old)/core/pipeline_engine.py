"""
Advanced Pipeline Execution Engine

Provides a flexible, configurable pipeline processing system 
with comprehensive performance tracking and error handling.
"""

import asyncio
import time
import uuid
from typing import Any, Callable, Dict, List, Optional
from dataclasses import dataclass, field

from src.shared.error_handling.logging import get_logger

# Constants
MAX_PIPELINE_STAGES = 20
MAX_EXECUTION_TIME = 60  # seconds
DEFAULT_TIMEOUT = 30  # seconds

# Create logger
logger = get_logger(__name__)

# Pipeline stage decorator
def pipeline_stage(stage_name: str = None, timeout: int = DEFAULT_TIMEOUT):
    """Decorator for pipeline stages"""
    def decorator(func):
        async def wrapper(self, *args, **kwargs):
            stage = stage_name or func.__name__
            self.current_stage = stage
            self.logger.info(f"Starting pipeline stage: {stage}")
            
            start_time = time.time()
            try:
                # Execute with timeout
                result = await asyncio.wait_for(
                    func(self, *args, **kwargs),
                    timeout=timeout
                )
                
                execution_time = time.time() - start_time
                self.stage_times[stage] = execution_time
                self.logger.info(f"Completed stage: {stage}", execution_time=f"{execution_time:.2f}s")
                return result
            
            except asyncio.TimeoutError:
                execution_time = time.time() - start_time
                self.logger.error(f"Stage timeout: {stage}", timeout=timeout, execution_time=f"{execution_time:.2f}s")
                self.errors.append(f"Stage {stage} timed out after {timeout} seconds")
                raise
            
            except Exception as e:
                execution_time = time.time() - start_time
                self.logger.error(f"Stage error: {stage}", error=str(e), execution_time=f"{execution_time:.2f}s")
                self.errors.append(f"Error in stage {stage}: {str(e)}")
                raise
        
        # Preserve metadata
        wrapper.__name__ = func.__name__
        wrapper.__doc__ = func.__doc__
        wrapper.is_pipeline_stage = True
        wrapper.stage_name = stage_name or func.__name__
        
        return wrapper
    return decorator

@dataclass
class PipelineContext:
    """Shared context for pipeline execution"""
    request_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    start_time: float = field(default_factory=time.time)
    data: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def add_data(self, key: str, value: Any) -> None:
        """Add data to the context"""
        self.data[key] = value
    
    def get_data(self, key: str, default: Any = None) -> Any:
        """Get data from the context"""
        return self.data.get(key, default)
    
    def add_metadata(self, key: str, value: Any) -> None:
        """Add metadata to the context"""
        self.metadata[key] = value
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """Get metadata from the context"""
        return self.metadata.get(key, default)
    
    def get_execution_time(self) -> float:
        """Get total execution time"""
        return time.time() - self.start_time

class Pipeline:
    """Base class for all pipelines"""
    
    def __init__(self, name: str = None, background_tasks = None, config: Dict[str, Any] = None):
        self.name = name or self.__class__.__name__
        self.background_tasks = background_tasks
        self.config = config or {}
        self.context = PipelineContext()
        self.logger = get_logger(f"pipeline.{self.name.lower()}")
        self.current_stage = None
        self.stages: List[str] = []
        self.errors: List[str] = []
        self.stage_times: Dict[str, float] = {}
        self.total_time: float = 0
        self.is_completed = False
        self.result = None
        
        # Discover pipeline stages
        self._discover_stages()
    
    def _discover_stages(self) -> None:
        """Discover pipeline stages from class methods"""
        for attr_name in dir(self):
            attr = getattr(self, attr_name)
            if callable(attr) and hasattr(attr, 'is_pipeline_stage'):
                self.stages.append(attr.stage_name)
    
    async def execute(self, **kwargs) -> Any:
        """Execute the pipeline"""
        self.logger.info(f"Starting pipeline execution: {self.name}")
        start_time = time.time()
        
        try:
            # Execute the run method
            self.result = await self.run(**kwargs)
            self.is_completed = True
            
            # Calculate total time
            self.total_time = time.time() - start_time
            self.logger.info(
                f"Pipeline completed: {self.name}",
                total_time=f"{self.total_time:.2f}s",
                stages=len(self.stages),
                errors=len(self.errors)
            )
            
            return self.result
        
        except Exception as e:
            self.total_time = time.time() - start_time
            self.logger.error(
                f"Pipeline failed: {self.name}",
                error=str(e),
                total_time=f"{self.total_time:.2f}s",
                current_stage=self.current_stage
            )
            self.errors.append(f"Pipeline execution error: {str(e)}")
            raise
    
    async def run(self, **kwargs) -> Any:
        """
        Main pipeline execution method.
        Should be implemented by subclasses.
        """
        raise NotImplementedError("Subclasses must implement run() method")
    
    def add_background_task(self, func: Callable, *args, **kwargs) -> None:
        """Add a task to be executed in the background"""
        if self.background_tasks:
            self.logger.info(f"Adding background task: {func.__name__}")
            self.background_tasks.add_task(func, *args, **kwargs)
        else:
            self.logger.warning(f"Cannot add background task: {func.__name__}, no background_tasks provided")
    
    def get_summary(self) -> Dict[str, Any]:
        """Get pipeline execution summary"""
        return {
            "name": self.name,
            "request_id": self.context.request_id,
            "is_completed": self.is_completed,
            "total_time": f"{self.total_time:.2f}s",
            "stages": self.stages,
            "stage_times": {k: f"{v:.2f}s" for k, v in self.stage_times.items()},
            "errors": self.errors,
            "metadata": self.context.metadata
        }

# Alias for backward compatibility
PipelineEngine = Pipeline

# Backwards-compatibility helpers
@dataclass
class PipelineStage:
    """Lightweight representation of a pipeline stage for compatibility."""
    name: str
    handler: Callable[..., Any]
    depends_on: List[str] = field(default_factory=list)

class PipelineBuilder:
    """Small helper to build a simple Pipeline from stages.

    Usage:
        builder = PipelineBuilder(name="MyPipeline")
        builder.add_stage("fetch", fetch_fn)
        builder.add_stage("process", process_fn, depends_on=["fetch"])
        pipeline = builder.build()
    """
    def __init__(self, name: Optional[str] = None, config: Optional[Dict[str, Any]] = None):
        self.name = name
        self.config = config or {}
        self._stages: List[PipelineStage] = []

    def add_stage(self, name: str, handler: Callable[..., Any], depends_on: Optional[List[str]] = None):
        self._stages.append(PipelineStage(name=name, handler=handler, depends_on=depends_on or []))
        return self

    def build(self) -> Pipeline:
        stages = list(self._stages)
        config = self.config

        class BuiltPipeline(Pipeline):
            async def run(self, **kwargs):
                # Execute stages in order; respect coroutine handlers
                for stage in stages:
                    handler = stage.handler
                    if asyncio.iscoroutinefunction(handler):
                        await handler(self, **kwargs)
                    else:
                        handler(self, **kwargs)
                return self.context.data

        return BuiltPipeline(name=self.name, config=config)