"""
Compatibility shim for core logging.
Re-exports logging utilities from `src.shared.error_handling.logging`.
"""
from src.shared.error_handling.logging import (
    get_logger,
    configure_logging,
    generate_correlation_id,
    get_pipeline_logger,
    get_trading_logger,
    log_request,
    log_error
)

__all__ = [
    "get_logger",
    "configure_logging",
    "generate_correlation_id",
    "get_pipeline_logger",
    "get_trading_logger",
    "log_request",
    "log_error"
] 