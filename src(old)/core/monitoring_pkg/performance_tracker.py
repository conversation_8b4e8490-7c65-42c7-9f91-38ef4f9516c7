"""
Performance Tracking Module

Provides utilities for tracking and analyzing performance metrics
for various components of the trading bot system.
"""

import time
import functools
import logging
from typing import Any, Callable, Dict, List, Optional
from datetime import datetime
import asyncio

from ..logger import get_logger

logger = get_logger(__name__)

class PerformanceTracker:
    """
    Performance tracking utility for monitoring execution times
    and resource usage across the application.
    """
    
    def __init__(self):
        """Initialize the performance tracker"""
        self.metrics = {}
        self.execution_times = {}
        self.thresholds = {
            'warning': 1.0,  # 1 second
            'critical': 5.0   # 5 seconds
        }
    
    def track_performance(self, func):
        """
        Decorator to track performance of a function
        
        Args:
            func: Function to track
            
        Returns:
            Wrapped function with performance tracking
        """
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                self._record_execution_time(func.__name__, time.time() - start_time)
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                self._record_execution_time(func.__name__, time.time() - start_time)
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    def _record_execution_time(self, function_name: str, execution_time: float):
        """
        Record execution time for a function
        
        Args:
            function_name: Name of the function
            execution_time: Execution time in seconds
        """
        if function_name not in self.execution_times:
            self.execution_times[function_name] = []
        
        self.execution_times[function_name].append(execution_time)
        
        # Trim the list to keep only the last 100 executions
        if len(self.execution_times[function_name]) > 100:
            self.execution_times[function_name] = self.execution_times[function_name][-100:]
        
        # Log slow executions
        if execution_time > self.thresholds['critical']:
            logger.warning(
                f"Critical performance: {function_name} took {execution_time:.2f}s",
                extra={
                    'function': function_name,
                    'execution_time': execution_time,
                    'threshold': 'critical'
                }
            )
        elif execution_time > self.thresholds['warning']:
            logger.info(
                f"Slow performance: {function_name} took {execution_time:.2f}s",
                extra={
                    'function': function_name,
                    'execution_time': execution_time,
                    'threshold': 'warning'
                }
            )
    
    def get_average_execution_time(self, function_name: str) -> Optional[float]:
        """
        Get average execution time for a function
        
        Args:
            function_name: Name of the function
            
        Returns:
            Average execution time in seconds, or None if no data
        """
        if function_name not in self.execution_times or not self.execution_times[function_name]:
            return None
        
        return sum(self.execution_times[function_name]) / len(self.execution_times[function_name])
    
    def get_performance_report(self) -> Dict[str, Any]:
        """
        Generate a performance report
        
        Returns:
            Dictionary with performance metrics
        """
        report = {
            'timestamp': datetime.now().isoformat(),
            'functions': {}
        }
        
        for func_name, times in self.execution_times.items():
            if not times:
                continue
                
            report['functions'][func_name] = {
                'count': len(times),
                'average': sum(times) / len(times),
                'min': min(times),
                'max': max(times),
                'latest': times[-1]
            }
        
        return report
    
    def set_threshold(self, level: str, value: float):
        """
        Set performance threshold
        
        Args:
            level: Threshold level ('warning' or 'critical')
            value: Threshold value in seconds
        """
        if level in self.thresholds:
            self.thresholds[level] = value
    
    def reset_metrics(self):
        """Reset all performance metrics"""
        self.execution_times = {}
        self.metrics = {}
