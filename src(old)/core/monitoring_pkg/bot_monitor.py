"""
Bot Health Monitoring Configuration
Handles the integration between bot and monitoring system
"""

from typing import Optional, Any, Dict, List
from datetime import datetime
import os
import json
import time

class SystemMonitor:
    """System monitoring for the bot and API"""
    
    def __init__(self):
        """Initialize the system monitor"""
        self.start_time = time.time()
        self.metrics = {
            "uptime_seconds": 0,
            "request_count": 0,
            "error_count": 0,
            "average_response_time": 0.0,
            "last_check": datetime.now().isoformat()
        }
        self.component_status = {
            "bot": "unknown",
            "api": "unknown",
            "database": "unknown",
            "redis": "unknown"
        }
        
    def update_uptime(self):
        """Update the uptime metric"""
        self.metrics["uptime_seconds"] = int(time.time() - self.start_time)
        self.metrics["last_check"] = datetime.now().isoformat()
        
    def record_request(self, response_time: float, success: bool = True):
        """
        Record a request and its response time
        
        Args:
            response_time: Request response time in seconds
            success: Whether the request was successful
        """
        self.metrics["request_count"] += 1
        
        if not success:
            self.metrics["error_count"] += 1
            
        # Update average response time
        current_avg = self.metrics["average_response_time"]
        request_count = self.metrics["request_count"]
        
        if request_count > 1:
            self.metrics["average_response_time"] = (
                (current_avg * (request_count - 1) + response_time) / request_count
            )
        else:
            self.metrics["average_response_time"] = response_time
            
    def update_component_status(self, component: str, status: str):
        """
        Update the status of a system component
        
        Args:
            component: Component name (bot, api, database, redis)
            status: Status (online, offline, degraded)
        """
        if component in self.component_status:
            self.component_status[component] = status
            
    def get_status(self) -> Dict[str, Any]:
        """
        Get the current system status
        
        Returns:
            Dict with system status information
        """
        self.update_uptime()
        
        return {
            "metrics": self.metrics,
            "components": self.component_status,
            "overall_status": self._calculate_overall_status()
        }
        
    def _calculate_overall_status(self) -> str:
        """
        Calculate the overall system status
        
        Returns:
            Overall status (healthy, degraded, critical)
        """
        statuses = list(self.component_status.values())
        
        if "offline" in statuses:
            return "critical"
        elif "degraded" in statuses:
            return "degraded"
        elif all(status == "online" for status in statuses):
            return "healthy"
        else:
            return "unknown"

    def health_check(self) -> Dict[str, Any]:
        """
        Run a quick health check and return a structured result.
        This exposes a small set of metrics consumed by the API /health endpoint.
        """
        try:
            self.update_uptime()
            overall = self._calculate_overall_status()
            details = {
                "uptime_seconds": self.metrics.get("uptime_seconds", 0),
                "request_count": self.metrics.get("request_count", 0),
                "error_count": self.metrics.get("error_count", 0),
                "average_response_time": self.metrics.get("average_response_time", 0.0),
                "components": self.component_status,
                "overall_status": overall,
                "last_check": self.metrics.get("last_check")
            }
            healthy = (overall == "healthy") and (details["error_count"] == 0)
            return {"healthy": healthy, "details": details}
        except Exception as e:
            return {"healthy": False, "details": {"error": str(e)}}

class ResponseMetricsTracker:
    """Tracks metrics for bot responses"""
    
    def __init__(self, metrics_file: str = None):
        """
        Initialize the response metrics tracker
        
        Args:
            metrics_file: Path to the metrics file
        """
        self.metrics_file = metrics_file or os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))),
            "data", "response_metrics.json"
        )
        self.metrics = self._load_metrics()
        
    def _load_metrics(self) -> Dict[str, Any]:
        """
        Load metrics from file
        
        Returns:
            Dict with metrics data
        """
        try:
            if os.path.exists(self.metrics_file):
                with open(self.metrics_file, 'r') as f:
                    return json.load(f)
            else:
                return {
                    "total_responses": 0,
                    "average_response_time": 0.0,
                    "command_metrics": {},
                    "hourly_usage": {},
                    "daily_usage": {},
                    "user_metrics": {}
                }
        except Exception as e:
            print(f"Error loading metrics: {e}")
            return {
                "total_responses": 0,
                "average_response_time": 0.0,
                "command_metrics": {},
                "hourly_usage": {},
                "daily_usage": {},
                "user_metrics": {}
            }
            
    def _save_metrics(self):
        """Save metrics to file"""
        try:
            os.makedirs(os.path.dirname(self.metrics_file), exist_ok=True)
            with open(self.metrics_file, 'w') as f:
                json.dump(self.metrics, f, indent=2)
        except Exception as e:
            print(f"Error saving metrics: {e}")
            
    def record_response(self, command: str, response_time: float, user_id: str = None):
        """
        Record a bot response
        
        Args:
            command: Command name
            response_time: Response time in seconds
            user_id: User ID (optional)
        """
        # Update total responses and average response time
        current_total = self.metrics["total_responses"]
        current_avg = self.metrics["average_response_time"]
        
        self.metrics["total_responses"] += 1
        
        if current_total > 0:
            self.metrics["average_response_time"] = (
                (current_avg * current_total + response_time) / (current_total + 1)
            )
        else:
            self.metrics["average_response_time"] = response_time
            
        # Update command metrics
        if command not in self.metrics["command_metrics"]:
            self.metrics["command_metrics"][command] = {
                "count": 0,
                "average_time": 0.0
            }
            
        cmd_metrics = self.metrics["command_metrics"][command]
        cmd_count = cmd_metrics["count"]
        cmd_avg = cmd_metrics["average_time"]
        
        cmd_metrics["count"] += 1
        
        if cmd_count > 0:
            cmd_metrics["average_time"] = (
                (cmd_avg * cmd_count + response_time) / (cmd_count + 1)
            )
        else:
            cmd_metrics["average_time"] = response_time
            
        # Update hourly usage
        hour = datetime.now().strftime("%H")
        if hour not in self.metrics["hourly_usage"]:
            self.metrics["hourly_usage"][hour] = 0
        self.metrics["hourly_usage"][hour] += 1
        
        # Update daily usage
        day = datetime.now().strftime("%Y-%m-%d")
        if day not in self.metrics["daily_usage"]:
            self.metrics["daily_usage"][day] = 0
        self.metrics["daily_usage"][day] += 1
        
        # Update user metrics if user_id provided
        if user_id:
            if user_id not in self.metrics["user_metrics"]:
                self.metrics["user_metrics"][user_id] = {
                    "count": 0,
                    "commands": {}
                }
                
            self.metrics["user_metrics"][user_id]["count"] += 1
            
            user_commands = self.metrics["user_metrics"][user_id]["commands"]
            if command not in user_commands:
                user_commands[command] = 0
            user_commands[command] += 1
            
        # Save metrics
        self._save_metrics()
        
    def get_metrics(self) -> Dict[str, Any]:
        """
        Get the current metrics
        
        Returns:
            Dict with metrics data
        """
        return self.metrics
        
    def get_command_metrics(self, command: str) -> Dict[str, Any]:
        """
        Get metrics for a specific command
        
        Args:
            command: Command name
            
        Returns:
            Dict with command metrics
        """
        if command in self.metrics["command_metrics"]:
            return self.metrics["command_metrics"][command]
        else:
            return {"count": 0, "average_time": 0.0}
            
    def get_user_metrics(self, user_id: str) -> Dict[str, Any]:
        """
        Get metrics for a specific user
        
        Args:
            user_id: User ID
            
        Returns:
            Dict with user metrics
        """
        if user_id in self.metrics["user_metrics"]:
            return self.metrics["user_metrics"][user_id]
        else:
            return {"count": 0, "commands": {}}

# Global monitoring instances
system_monitor = SystemMonitor()
response_metrics_tracker = ResponseMetricsTracker()

class BotMonitorConfig:
    """Configuration for bot health monitoring"""
    
    @staticmethod
    def initialize_bot_monitoring(bot_instance: Any):
        """
        Initialize bot health monitoring
        
        Args:
            bot_instance: The trading bot instance to monitor
        """
        # Set the bot instance for health monitoring
        from src.api.routes.bot_health import bot_health_monitor
        bot_health_monitor.set_bot_instance(bot_instance)
        
        # Log initialization
        bot_instance.logger.info("Bot health monitoring initialized")
        
        # Update component status
        system_monitor.update_component_status("bot", "online")
    
    @staticmethod
    async def get_health_summary() -> Dict[str, Any]:
        """
        Get a quick health summary for the bot
        
        Returns:
            Dict with essential health information
        """
        from src.api.routes.bot_health import detailed_health_check
        
        try:
            health_data = await detailed_health_check()
            return {
                "status": health_data["overall_status"],
                "bot_connected": health_data["details"]["bot"]["bot"]["connected"],
                "uptime": health_data["details"]["bot"]["uptime"]["process"],
                "timestamp": health_data["timestamp"]
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "timestamp": str(datetime.utcnow())
            }

# Global monitoring instance
bot_monitor = BotMonitorConfig()