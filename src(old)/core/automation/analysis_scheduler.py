"""
Analysis Job Scheduler - Core of Automated Analysis System

Manages automated analysis jobs with priority queues, rate-limit awareness,
and intelligent job execution scheduling.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum
from queue import PriorityQueue as SyncPriorityQueue
import heapq

logger = logging.getLogger(__name__)


class JobPriority(Enum):
    """Job priority levels for analysis scheduling."""
    CRITICAL = 1    # Immediate execution (system alerts, user requests)
    HIGH = 2        # High priority watchlist symbols
    MEDIUM = 3      # Standard watchlist symbols
    LOW = 4         # Background analysis, system scans


class JobStatus(Enum):
    """Job execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class AnalysisJob:
    """Represents an analysis job to be executed."""
    job_id: str
    symbol: str
    user_id: Optional[str] = None
    priority: JobPriority = JobPriority.MEDIUM
    analysis_depth: str = "standard"  # quick, standard, deep_dive
    scheduled_at: datetime = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    status: JobStatus = JobStatus.PENDING
    retry_count: int = 0
    max_retries: int = 3
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.scheduled_at is None:
            self.scheduled_at = datetime.now()
    
    def __lt__(self, other):
        """Priority queue comparison - lower priority numbers = higher priority."""
        if self.priority.value != other.priority.value:
            return self.priority.value < other.priority.value
        # If same priority, earlier scheduled jobs go first
        return self.scheduled_at < other.scheduled_at
    
    def start_execution(self):
        """Mark job as started."""
        self.status = JobStatus.RUNNING
        self.started_at = datetime.now()
        logger.info(f"Started execution of job {self.job_id} for {self.symbol}")
    
    def complete_success(self, result: Dict[str, Any]):
        """Mark job as successfully completed."""
        self.status = JobStatus.COMPLETED
        self.completed_at = datetime.now()
        self.result = result
        logger.info(f"Completed job {self.job_id} for {self.symbol} successfully")
    
    def complete_failure(self, error_message: str):
        """Mark job as failed."""
        self.status = JobStatus.FAILED
        self.completed_at = datetime.now()
        self.error_message = error_message
        logger.error(f"Failed job {self.job_id} for {self.symbol}: {error_message}")
    
    def can_retry(self) -> bool:
        """Check if job can be retried."""
        return self.retry_count < self.max_retries and self.status == JobStatus.FAILED
    
    def retry(self):
        """Retry the job."""
        self.retry_count += 1
        self.status = JobStatus.PENDING
        self.started_at = None
        self.completed_at = None
        self.error_message = None
        self.result = None
        logger.info(f"Retrying job {self.job_id} for {self.symbol} (attempt {self.retry_count})")


class AsyncPriorityQueue:
    """Asynchronous priority queue for job management."""
    
    def __init__(self):
        self._queue = []
        self._lock = asyncio.Lock()
    
    async def put(self, job: AnalysisJob):
        """Add a job to the queue."""
        async with self._lock:
            heapq.heappush(self._queue, job)
            logger.debug(f"Added job {job.job_id} to queue (priority: {job.priority.value})")
    
    async def get(self) -> Optional[AnalysisJob]:
        """Get the highest priority job from the queue."""
        async with self._lock:
            if self._queue:
                job = heapq.heappop(self._queue)
                logger.debug(f"Retrieved job {job.job_id} from queue")
                return job
            return None
    
    async def peek(self) -> Optional[AnalysisJob]:
        """Peek at the highest priority job without removing it."""
        async with self._lock:
            if self._queue:
                return self._queue[0]
            return None
    
    async def size(self) -> int:
        """Get the current queue size."""
        async with self._lock:
            return len(self._queue)
    
    async def empty(self) -> bool:
        """Check if queue is empty."""
        async with self._lock:
            return len(self._queue) == 0


class RateLimitManager:
    """Manages API rate limits across multiple providers."""
    
    def __init__(self):
        self.providers = {
            "polygon": {"calls_per_minute": 5, "current_calls": 0, "reset_time": None},
            "finnhub": {"calls_per_minute": 30, "current_calls": 0, "reset_time": None},
            "alpha_vantage": {"calls_per_minute": 5, "current_calls": 0, "reset_time": None},
            "yfinance": {"calls_per_minute": float('inf'), "current_calls": 0, "reset_time": None}
        }
        self.fallback_order = ["polygon", "finnhub", "alpha_vantage", "yfinance"]
        self._lock = asyncio.Lock()
    
    async def can_make_request(self, provider: str = None) -> bool:
        """Check if we can make a request to any provider."""
        async with self._lock:
            if provider:
                return await self._check_provider_limit(provider)
            
            # Check if any provider is available
            for provider_name in self.fallback_order:
                if await self._check_provider_limit(provider_name):
                    return True
            return False
    
    async def _check_provider_limit(self, provider: str) -> bool:
        """Check if specific provider has available calls."""
        if provider not in self.providers:
            return False
        
        provider_info = self.providers[provider]
        now = datetime.now()
        
        # Reset counter if minute has passed
        if provider_info["reset_time"] and now >= provider_info["reset_time"]:
            provider_info["current_calls"] = 0
            provider_info["reset_time"] = now + timedelta(minutes=1)
        
        # Set reset time if not set
        if not provider_info["reset_time"]:
            provider_info["reset_time"] = now + timedelta(minutes=1)
        
        return provider_info["current_calls"] < provider_info["calls_per_minute"]
    
    async def record_request(self, provider: str):
        """Record a successful API request."""
        async with self._lock:
            if provider in self.providers:
                self.providers[provider]["current_calls"] += 1
                logger.debug(f"Recorded API request to {provider} (calls: {self.providers[provider]['current_calls']})")
    
    async def get_available_providers(self) -> List[str]:
        """Get list of providers with available API calls."""
        async with self._lock:
            available = []
            for provider_name in self.fallback_order:
                if await self._check_provider_limit(provider_name):
                    available.append(provider_name)
            return available


class AnalysisJobScheduler:
    """Centralized scheduler for automated analysis jobs."""
    
    def __init__(self, max_concurrent_jobs: int = 5):
        self.max_concurrent_jobs = max_concurrent_jobs
        self.job_queue = AsyncPriorityQueue()
        self.rate_limit_manager = RateLimitManager()
        self.running_jobs: Dict[str, AnalysisJob] = {}
        self.completed_jobs: List[AnalysisJob] = []
        self.failed_jobs: List[AnalysisJob] = []
        self.job_executor: Optional[Callable] = None
        self.is_running = False
        self._lock = asyncio.Lock()
        
        # Statistics
        self.total_jobs_processed = 0
        self.successful_jobs = 0
        self.failed_jobs_count = 0
        self.average_job_duration = 0.0
    
    def set_job_executor(self, executor: Callable):
        """Set the function that will execute analysis jobs."""
        self.job_executor = executor
        logger.info("Job executor set for analysis scheduler")
    
    async def schedule_job(self, symbol: str, user_id: Optional[str] = None,
                          priority: JobPriority = JobPriority.MEDIUM,
                          analysis_depth: str = "standard") -> str:
        """Schedule a new analysis job."""
        job_id = f"job_{symbol}_{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        job = AnalysisJob(
            job_id=job_id,
            symbol=symbol.upper(),
            user_id=user_id,
            priority=priority,
            analysis_depth=analysis_depth
        )
        
        await self.job_queue.put(job)
        logger.info(f"Scheduled analysis job {job_id} for {symbol} (priority: {priority.value})")
        return job_id
    
    async def schedule_watchlist_analysis(self, user_id: str, watchlist_symbols: List[Dict[str, Any]]):
        """Schedule analysis for multiple watchlist symbols."""
        scheduled_jobs = []
        
        for symbol_info in watchlist_symbols:
            symbol = symbol_info.get('symbol')
            priority = self._convert_priority(symbol_info.get('priority', 'medium'))
            analysis_depth = symbol_info.get('analysis_depth', 'standard')
            
            if symbol:
                job_id = await self.schedule_job(
                    symbol=symbol,
                    user_id=user_id,
                    priority=priority,
                    analysis_depth=analysis_depth
                )
                scheduled_jobs.append(job_id)
        
        logger.info(f"Scheduled {len(scheduled_jobs)} watchlist analysis jobs for user {user_id}")
        return scheduled_jobs
    
    def _convert_priority(self, priority_str: str) -> JobPriority:
        """Convert string priority to JobPriority enum."""
        priority_map = {
            'high': JobPriority.HIGH,
            'medium': JobPriority.MEDIUM,
            'low': JobPriority.LOW,
            'critical': JobPriority.CRITICAL
        }
        return priority_map.get(priority_str.lower(), JobPriority.MEDIUM)
    
    async def start_scheduler(self):
        """Start the job scheduler."""
        if self.is_running:
            logger.warning("Scheduler is already running")
            return
        
        if not self.job_executor:
            logger.error("No job executor set. Cannot start scheduler.")
            return
        
        self.is_running = True
        logger.info("Starting analysis job scheduler")
        
        # Start the main scheduler loop
        asyncio.create_task(self._scheduler_loop())
    
    async def stop_scheduler(self):
        """Stop the job scheduler."""
        self.is_running = False
        logger.info("Stopping analysis job scheduler")
        
        # Wait for running jobs to complete
        while self.running_jobs:
            await asyncio.sleep(1)
        
        logger.info("Analysis job scheduler stopped")
    
    async def _scheduler_loop(self):
        """Main scheduler loop that processes jobs."""
        while self.is_running:
            try:
                # Check if we can process more jobs
                if len(self.running_jobs) < self.max_concurrent_jobs:
                    # Get next job from queue
                    job = await self.job_queue.get()
                    if job:
                        # Check if we can make API requests
                        if await self.rate_limit_manager.can_make_request():
                            # Start job execution
                            asyncio.create_task(self._execute_job(job))
                        else:
                            # Put job back in queue and wait
                            await self.job_queue.put(job)
                            await asyncio.sleep(10)  # Wait 10 seconds before checking again
                    else:
                        # No jobs in queue, wait a bit
                        await asyncio.sleep(5)
                else:
                    # At capacity, wait for jobs to complete
                    await asyncio.sleep(1)
                    
            except Exception as e:
                logger.error(f"Error in scheduler loop: {e}", exc_info=True)
                await asyncio.sleep(5)
    
    async def _execute_job(self, job: AnalysisJob):
        """Execute a single analysis job."""
        try:
            # Mark job as running
            job.start_execution()
            self.running_jobs[job.job_id] = job
            
            # Execute the job
            if self.job_executor:
                result = await self.job_executor(job)
                job.complete_success(result)
                
                # Update statistics
                self.total_jobs_processed += 1
                self.successful_jobs += 1
                
                # Calculate duration
                if job.started_at and job.completed_at:
                    duration = (job.completed_at - job.started_at).total_seconds()
                    self._update_average_duration(duration)
                
            else:
                raise RuntimeError("No job executor available")
                
        except Exception as e:
            error_msg = f"Job execution failed: {str(e)}"
            job.complete_failure(error_msg)
            
            # Update statistics
            self.total_jobs_processed += 1
            self.failed_jobs_count += 1
            
            # Handle retries
            if job.can_retry():
                logger.info(f"Retrying job {job.job_id} for {job.symbol}")
                job.retry()
                await self.job_queue.put(job)
            else:
                logger.error(f"Job {job.job_id} failed permanently after {job.max_retries} retries")
                self.failed_jobs.append(job)
        
        finally:
            # Remove from running jobs
            if job.job_id in self.running_jobs:
                del self.running_jobs[job.job_id]
            
            # Add to completed jobs if successful
            if job.status == JobStatus.COMPLETED:
                self.completed_jobs.append(job)
    
    def _update_average_duration(self, new_duration: float):
        """Update average job duration."""
        if self.total_jobs_processed == 1:
            self.average_job_duration = new_duration
        else:
            # Exponential moving average
            alpha = 0.1
            self.average_job_duration = (alpha * new_duration) + ((1 - alpha) * self.average_job_duration)
    
    async def get_scheduler_status(self) -> Dict[str, Any]:
        """Get current scheduler status and statistics."""
        queue_size = await self.job_queue.size()
        
        return {
            'is_running': self.is_running,
            'queue_size': queue_size,
            'running_jobs': len(self.running_jobs),
            'completed_jobs': len(self.completed_jobs),
            'failed_jobs': len(self.failed_jobs),
            'total_processed': self.total_jobs_processed,
            'success_rate': self.successful_jobs / max(self.total_jobs_processed, 1),
            'average_duration': self.average_job_duration,
            'max_concurrent': self.max_concurrent_jobs
        }
    
    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a pending or running job."""
        # Check if job is running
        if job_id in self.running_jobs:
            job = self.running_jobs[job_id]
            job.status = JobStatus.CANCELLED
            del self.running_jobs[job_id]
            logger.info(f"Cancelled running job {job_id}")
            return True
        
        # Check if job is in queue (this is more complex with async queue)
        # For now, we'll just log that we can't cancel queued jobs
        logger.warning(f"Cannot cancel queued job {job_id} - not implemented yet")
        return False
    
    async def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a specific job."""
        # Check running jobs
        if job_id in self.running_jobs:
            job = self.running_jobs[job_id]
            return {
                'job_id': job.job_id,
                'symbol': job.symbol,
                'status': job.status.value,
                'started_at': job.started_at.isoformat() if job.started_at else None,
                'priority': job.priority.value
            }
        
        # Check completed jobs
        for job in self.completed_jobs:
            if job.job_id == job_id:
                return {
                    'job_id': job.job_id,
                    'symbol': job.symbol,
                    'status': job.status.value,
                    'started_at': job.started_at.isoformat() if job.started_at else None,
                    'completed_at': job.completed_at.isoformat() if job.completed_at else None,
                    'priority': job.priority.value,
                    'result': job.result if job.status == JobStatus.COMPLETED else None,
                    'error_message': job.error_message if job.status == JobStatus.FAILED else None
                }
        
        # Check failed jobs
        for job in self.failed_jobs:
            if job.job_id == job_id:
                return {
                    'job_id': job.job_id,
                    'symbol': job.symbol,
                    'status': job.status.value,
                    'started_at': job.started_at.isoformat() if job.started_at else None,
                    'completed_at': job.completed_at.isoformat() if job.completed_at else None,
                    'priority': job.priority.value,
                    'error_message': job.error_message,
                    'retry_count': job.retry_count
                }
        
        return None


# Global scheduler instance
_global_scheduler: Optional[AnalysisJobScheduler] = None


def get_global_scheduler() -> AnalysisJobScheduler:
    """Get the global analysis job scheduler instance."""
    global _global_scheduler
    if _global_scheduler is None:
        _global_scheduler = AnalysisJobScheduler()
    return _global_scheduler


async def start_global_scheduler():
    """Start the global analysis job scheduler."""
    scheduler = get_global_scheduler()
    await scheduler.start_scheduler()
    return scheduler


async def stop_global_scheduler():
    """Stop the global analysis job scheduler."""
    global _global_scheduler
    if _global_scheduler:
        await _global_scheduler.stop_scheduler()
        _global_scheduler = None 