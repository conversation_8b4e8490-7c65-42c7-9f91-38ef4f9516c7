"""
Report Formatter

Converts generated reports to different output formats including Discord,
markdown, and email. Provides consistent formatting across all output types.
"""

import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

from .report_engine import GeneratedReport, ReportType, AIInsight, MarketData, SectorPerformance, MarketHealth

logger = logging.getLogger(__name__)


class OutputFormat(Enum):
    """Supported output formats."""
    DISCORD = "discord"
    MARKDOWN = "markdown"
    EMAIL = "email"
    JSON = "json"


@dataclass
class FormattedReport:
    """Formatted report for specific output."""
    format: OutputFormat
    content: str
    metadata: Dict[str, Any]
    timestamp: datetime


class ReportFormatter:
    """
    Formats generated reports for different output channels.
    """
    
    def __init__(self):
        self.discord_emojis = self._load_discord_emojis()
        self.markdown_templates = self._load_markdown_templates()
    
    def _load_discord_emojis(self) -> Dict[str, str]:
        """Load Discord emojis for different report elements."""
        return {
            "market": "📊",
            "trend": "📈",
            "sector": "🏭",
            "health": "🏥",
            "insight": "🤖",
            "quality": "📊",
            "provider": "🔌",
            "recommendation": "💡",
            "risk": "⚠️",
            "opportunity": "🎯",
            "alert": "🚨",
            "summary": "📋",
            "performance": "🚀",
            "volume": "📊",
            "time": "🕐",
            "calendar": "📅"
        }
    
    def _load_markdown_templates(self) -> Dict[str, str]:
        """Load markdown templates for different report types."""
        return {
            ReportType.DAILY_MARKET_SUMMARY.value: self._get_daily_summary_markdown(),
            ReportType.MARKET_HEALTH_DASHBOARD.value: self._get_health_dashboard_markdown(),
            ReportType.ANOMALY_ALERT.value: self._get_anomaly_alert_markdown()
        }
    
    def _get_daily_summary_markdown(self) -> str:
        """Get daily summary markdown template."""
        return """
# {title}

## 📊 Market Summary
{summary}

## 🚀 Top Performers
{top_performers}

## 📉 Top Losers
{top_losers}

## 🏭 Sector Performance
{sector_performance}

## 🤖 AI Insights
{ai_insights}

## 📈 Data Quality
{data_quality}

## 🔌 Provider Reliability
{provider_reliability}

## 💡 Recommendations
{recommendations}

## ⚠️ Risk Alerts
{risk_alerts}

---
*Report generated automatically at {timestamp}*
"""
    
    def _get_health_dashboard_markdown(self) -> str:
        """Get health dashboard markdown template."""
        return """
# {title}

## 🏥 Market Health Overview
{summary}

## 📊 Data Quality Metrics
{data_quality}

## 🔌 Provider Performance
{provider_reliability}

## 🤖 Health Insights
{ai_insights}

## 💡 Health Recommendations
{recommendations}

## ⚠️ Health Alerts
{risk_alerts}

---
*Health report generated automatically at {timestamp}*
"""
    
    def _get_anomaly_alert_markdown(self) -> str:
        """Get anomaly alert markdown template."""
        return """
# 🚨 Anomaly Alert

## ⚠️ Detected Anomalies
{anomalies}

## 🔍 Analysis
{analysis}

## 💡 Recommendations
{recommendations}

## ⚠️ Risk Assessment
{risk_assessment}

---
*Alert generated automatically at {timestamp}*
"""
    
    def format_for_discord(self, report: GeneratedReport) -> FormattedReport:
        """Format report for Discord output."""
        try:
            emoji = self.discord_emojis
            
            if report.report_type == ReportType.DAILY_MARKET_SUMMARY:
                content = self._format_daily_summary_discord(report, emoji)
            elif report.report_type == ReportType.MARKET_HEALTH_DASHBOARD:
                content = self._format_health_dashboard_discord(report, emoji)
            elif report.report_type == ReportType.ANOMALY_ALERT:
                content = self._format_anomaly_alert_discord(report, emoji)
            else:
                content = self._format_generic_discord(report, emoji)
            
            return FormattedReport(
                format=OutputFormat.DISCORD,
                content=content,
                metadata={"report_type": report.report_type.value, "length": len(content)},
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error formatting report for Discord: {e}")
            return self._format_error_discord(report, e)
    
    def format_for_markdown(self, report: GeneratedReport) -> FormattedReport:
        """Format report for markdown output."""
        try:
            if report.report_type == ReportType.DAILY_MARKET_SUMMARY:
                content = self._format_daily_summary_markdown(report)
            elif report.report_type == ReportType.MARKET_HEALTH_DASHBOARD:
                content = self._format_health_dashboard_markdown(report)
            elif report.report_type == ReportType.ANOMALY_ALERT:
                content = self._format_anomaly_alert_markdown(report)
            else:
                content = self._format_generic_markdown(report)
            
            return FormattedReport(
                format=OutputFormat.MARKDOWN,
                content=content,
                metadata={"report_type": report.report_type.value, "length": len(content)},
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error formatting report for markdown: {e}")
            return self._format_error_markdown(report, e)
    
    def format_for_email(self, report: GeneratedReport) -> FormattedReport:
        """Format report for email output."""
        try:
            # Email format is similar to markdown but with HTML tags
            if report.report_type == ReportType.DAILY_MARKET_SUMMARY:
                content = self._format_daily_summary_email(report)
            elif report.report_type == ReportType.MARKET_HEALTH_DASHBOARD:
                content = self._format_health_dashboard_email(report)
            elif report.report_type == ReportType.ANOMALY_ALERT:
                content = self._format_anomaly_alert_email(report)
            else:
                content = self._format_generic_email(report)
            
            return FormattedReport(
                format=OutputFormat.EMAIL,
                content=content,
                metadata={"report_type": report.report_type.value, "length": len(content)},
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error formatting report for email: {e}")
            return self._format_error_email(report, e)
    
    def format_for_json(self, report: GeneratedReport) -> FormattedReport:
        """Format report for JSON output."""
        try:
            # Convert report to JSON-serializable format
            json_data = {
                "report_type": report.report_type.value,
                "timestamp": report.timestamp.isoformat(),
                "title": report.title,
                "summary": report.summary,
                "market_data": [
                    {
                        "symbol": m.symbol,
                        "current_price": m.current_price,
                        "price_change": m.price_change,
                        "price_change_pct": m.price_change_pct,
                        "volume": m.volume,
                        "sector": m.sector
                    } for m in report.market_data
                ],
                "sector_performance": [
                    {
                        "sector": s.sector,
                        "performance_pct": s.performance_pct,
                        "top_performer": s.top_performer,
                        "worst_performer": s.worst_performer
                    } for s in report.sector_performance
                ],
                "market_health": {
                    "avg_data_quality": report.market_health.avg_data_quality,
                    "stale_symbols_count": report.market_health.stale_symbols_count,
                    "total_symbols": report.market_health.total_symbols,
                    "risk_level": report.market_health.risk_level,
                    "market_sentiment": report.market_health.market_sentiment
                },
                "ai_insights": [
                    {
                        "insight_type": i.insight_type,
                        "description": i.description,
                        "confidence": i.confidence,
                        "actionable": i.actionable,
                        "recommendation": i.recommendation,
                        "risk_assessment": i.risk_assessment
                    } for i in report.ai_insights
                ],
                "data_quality_summary": report.data_quality_summary,
                "provider_reliability": report.provider_reliability,
                "recommendations": report.recommendations,
                "risk_alerts": report.risk_alerts
            }
            
            import json
            content = json.dumps(json_data, indent=2)
            
            return FormattedReport(
                format=OutputFormat.JSON,
                content=content,
                metadata={"report_type": report.report_type.value, "length": len(content)},
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error formatting report for JSON: {e}")
            return self._format_error_json(report, e)
    
    def _format_daily_summary_discord(self, report: GeneratedReport, emoji: Dict[str, str]) -> str:
        """Format daily summary report for Discord."""
        try:
            # Header
            content = f"{emoji['market']} **{report.title}**\n\n"
            
            # Summary
            content += f"{emoji['summary']} **Market Summary**\n{report.summary}\n\n"
            
            # Top performers
            if report.market_data:
                top_performers = sorted(report.market_data, key=lambda x: x.price_change_pct, reverse=True)[:5]
                content += f"{emoji['performance']} **Top Performers**\n"
                for stock in top_performers:
                    change_emoji = "🟢" if stock.price_change_pct > 0 else "🔴"
                    content += f"{change_emoji} **{stock.symbol}** {stock.price_change_pct:+.1f}% (${stock.current_price:.2f})\n"
                content += "\n"
            
            # Sector performance
            if report.sector_performance:
                content += f"{emoji['sector']} **Sector Performance**\n"
                for sector in report.sector_performance[:5]:
                    sector_emoji = "🟢" if sector.performance_pct > 0 else "🔴"
                    content += f"{sector_emoji} **{sector.sector}** {sector.performance_pct:+.1f}%\n"
                content += "\n"
            
            # AI insights
            if report.ai_insights:
                content += f"{emoji['insight']} **AI Insights**\n"
                for insight in report.ai_insights[:3]:  # Limit to top 3
                    content += f"• {insight.description}\n"
                content += "\n"
            
            # Data quality
            content += f"{emoji['quality']} **Data Quality**\n{report.data_quality_summary}\n\n"
            
            # Provider reliability
            content += f"{emoji['provider']} **Provider Reliability**\n{report.provider_reliability}\n\n"
            
            # Recommendations
            if report.recommendations:
                content += f"{emoji['recommendation']} **Key Recommendations**\n"
                for rec in report.recommendations[:3]:  # Limit to top 3
                    content += f"• {rec}\n"
                content += "\n"
            
            # Risk alerts
            if report.risk_alerts:
                content += f"{emoji['risk']} **Risk Alerts**\n"
                for alert in report.risk_alerts[:3]:  # Limit to top 3
                    content += f"{alert}\n"
                content += "\n"
            
            # Footer
            content += f"{emoji['time']} *Report generated at {report.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}*"
            
            return content
            
        except Exception as e:
            logger.error(f"Error formatting daily summary for Discord: {e}")
            return f"❌ Error formatting daily summary report: {str(e)}"
    
    def _format_health_dashboard_discord(self, report: GeneratedReport, emoji: Dict[str, str]) -> str:
        """Format health dashboard report for Discord."""
        try:
            # Header
            content = f"{emoji['health']} **{report.title}**\n\n"
            
            # Summary
            content += f"{emoji['summary']} **Health Overview**\n{report.summary}\n\n"
            
            # Data quality
            content += f"{emoji['quality']} **Data Quality**\n{report.data_quality_summary}\n\n"
            
            # Provider reliability
            content += f"{emoji['provider']} **Provider Performance**\n{report.provider_reliability}\n\n"
            
            # AI insights
            if report.ai_insights:
                content += f"{emoji['insight']} **Health Insights**\n"
                for insight in report.ai_insights[:3]:
                    content += f"• {insight.description}\n"
                content += "\n"
            
            # Recommendations
            if report.recommendations:
                content += f"{emoji['recommendation']} **Health Recommendations**\n"
                for rec in report.recommendations[:3]:
                    content += f"• {rec}\n"
                content += "\n"
            
            # Risk alerts
            if report.risk_alerts:
                content += f"{emoji['risk']} **Health Alerts**\n"
                for alert in report.risk_alerts[:3]:
                    content += f"{alert}\n"
                content += "\n"
            
            # Footer
            content += f"{emoji['time']} *Health report generated at {report.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}*"
            
            return content
            
        except Exception as e:
            logger.error(f"Error formatting health dashboard for Discord: {e}")
            return f"❌ Error formatting health dashboard report: {str(e)}"
    
    def _format_anomaly_alert_discord(self, report: GeneratedReport, emoji: Dict[str, str]) -> str:
        """Format anomaly alert report for Discord."""
        try:
            # Header
            content = f"{emoji['alert']} **🚨 Anomaly Alert**\n\n"
            
            # Summary
            content += f"{emoji['summary']} **Alert Summary**\n{report.summary}\n\n"
            
            # AI insights (anomalies)
            if report.ai_insights:
                content += f"{emoji['insight']} **Detected Anomalies**\n"
                for insight in report.ai_insights:
                    if insight.insight_type == "anomaly":
                        content += f"• {insight.description}\n"
                content += "\n"
            
            # Recommendations
            if report.recommendations:
                content += f"{emoji['recommendation']} **Immediate Actions**\n"
                for rec in report.recommendations[:3]:
                    content += f"• {rec}\n"
                content += "\n"
            
            # Risk assessment
            if report.ai_insights:
                content += f"{emoji['risk']} **Risk Assessment**\n"
                for insight in report.ai_insights:
                    if insight.risk_assessment:
                        content += f"• {insight.risk_assessment}\n"
                content += "\n"
            
            # Footer
            content += f"{emoji['time']} *Alert generated at {report.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}*"
            
            return content
            
        except Exception as e:
            logger.error(f"Error formatting anomaly alert for Discord: {e}")
            return f"❌ Error formatting anomaly alert: {str(e)}"
    
    def _format_generic_discord(self, report: GeneratedReport, emoji: Dict[str, str]) -> str:
        """Format generic report for Discord."""
        try:
            content = f"{emoji['market']} **{report.title}**\n\n"
            content += f"{emoji['summary']} **Summary**\n{report.summary}\n\n"
            
            if report.ai_insights:
                content += f"{emoji['insight']} **Key Insights**\n"
                for insight in report.ai_insights[:3]:
                    content += f"• {insight.description}\n"
                content += "\n"
            
            if report.recommendations:
                content += f"{emoji['recommendation']} **Recommendations**\n"
                for rec in report.recommendations[:3]:
                    content += f"• {rec}\n"
                content += "\n"
            
            content += f"{emoji['time']} *Report generated at {report.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}*"
            
            return content
            
        except Exception as e:
            logger.error(f"Error formatting generic report for Discord: {e}")
            return f"❌ Error formatting report: {str(e)}"
    
    def _format_daily_summary_markdown(self, report: GeneratedReport) -> str:
        """Format daily summary report for markdown."""
        try:
            content = f"# {report.title}\n\n"
            content += f"## 📊 Market Summary\n{report.summary}\n\n"
            
            # Top performers
            if report.market_data:
                top_performers = sorted(report.market_data, key=lambda x: x.price_change_pct, reverse=True)[:5]
                content += "## 🚀 Top Performers\n"
                for stock in top_performers:
                    content += f"- **{stock.symbol}**: {stock.price_change_pct:+.1f}% (${stock.current_price:.2f})\n"
                content += "\n"
            
            # Sector performance
            if report.sector_performance:
                content += "## 🏭 Sector Performance\n"
                for sector in report.sector_performance[:5]:
                    content += f"- **{sector.sector}**: {sector.performance_pct:+.1f}%\n"
                content += "\n"
            
            # AI insights
            if report.ai_insights:
                content += "## 🤖 AI Insights\n"
                for insight in report.ai_insights[:5]:
                    content += f"- **{insight.insight_type.title()}**: {insight.description}\n"
                    if insight.recommendation:
                        content += f"  - *Recommendation*: {insight.recommendation}\n"
                content += "\n"
            
            # Data quality
            content += f"## 📊 Data Quality\n{report.data_quality_summary}\n\n"
            
            # Provider reliability
            content += f"## 🔌 Provider Reliability\n{report.provider_reliability}\n\n"
            
            # Recommendations
            if report.recommendations:
                content += "## 💡 Key Recommendations\n"
                for rec in report.recommendations:
                    content += f"- {rec}\n"
                content += "\n"
            
            # Risk alerts
            if report.risk_alerts:
                content += "## ⚠️ Risk Alerts\n"
                for alert in report.risk_alerts:
                    content += f"- {alert}\n"
                content += "\n"
            
            # Footer
            content += f"---\n*Report generated at {report.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}*"
            
            return content
            
        except Exception as e:
            logger.error(f"Error formatting daily summary for markdown: {e}")
            return f"# Error\n\nUnable to format daily summary report: {str(e)}"
    
    def _format_health_dashboard_markdown(self, report: GeneratedReport) -> str:
        """Format health dashboard report for markdown."""
        try:
            content = f"# {report.title}\n\n"
            content += f"## 🏥 Market Health Overview\n{report.summary}\n\n"
            content += f"## 📊 Data Quality Metrics\n{report.data_quality_summary}\n\n"
            content += f"## 🔌 Provider Performance\n{report.provider_reliability}\n\n"
            
            # AI insights
            if report.ai_insights:
                content += "## 🤖 Health Insights\n"
                for insight in report.ai_insights:
                    content += f"- **{insight.insight_type.title()}**: {insight.description}\n"
                    if insight.recommendation:
                        content += f"  - *Recommendation*: {insight.recommendation}\n"
                content += "\n"
            
            # Recommendations
            if report.recommendations:
                content += "## 💡 Health Recommendations\n"
                for rec in report.recommendations:
                    content += f"- {rec}\n"
                content += "\n"
            
            # Risk alerts
            if report.risk_alerts:
                content += "## ⚠️ Health Alerts\n"
                for alert in report.risk_alerts:
                    content += f"- {alert}\n"
                content += "\n"
            
            content += f"---\n*Health report generated at {report.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}*"
            
            return content
            
        except Exception as e:
            logger.error(f"Error formatting health dashboard for markdown: {e}")
            return f"# Error\n\nUnable to format health dashboard report: {str(e)}"
    
    def _format_anomaly_alert_markdown(self, report: GeneratedReport) -> str:
        """Format anomaly alert report for markdown."""
        try:
            content = "# 🚨 Anomaly Alert\n\n"
            content += f"## ⚠️ Alert Summary\n{report.summary}\n\n"
            
            # AI insights (anomalies)
            if report.ai_insights:
                content += "## 🔍 Detected Anomalies\n"
                for insight in report.ai_insights:
                    if insight.insight_type == "anomaly":
                        content += f"- {insight.description}\n"
                        if insight.recommendation:
                            content += f"  - *Recommendation*: {insight.recommendation}\n"
                content += "\n"
            
            # Recommendations
            if report.recommendations:
                content += "## 💡 Immediate Actions\n"
                for rec in report.recommendations:
                    content += f"- {rec}\n"
                content += "\n"
            
            # Risk assessment
            if report.ai_insights:
                content += "## ⚠️ Risk Assessment\n"
                for insight in report.ai_insights:
                    if insight.risk_assessment:
                        content += f"- {insight.risk_assessment}\n"
                content += "\n"
            
            content += f"---\n*Alert generated at {report.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}*"
            
            return content
            
        except Exception as e:
            logger.error(f"Error formatting anomaly alert for markdown: {e}")
            return f"# Error\n\nUnable to format anomaly alert: {str(e)}"
    
    def _format_generic_markdown(self, report: GeneratedReport) -> str:
        """Format generic report for markdown."""
        try:
            content = f"# {report.title}\n\n"
            content += f"## 📋 Summary\n{report.summary}\n\n"
            
            if report.ai_insights:
                content += "## 🤖 Key Insights\n"
                for insight in report.ai_insights[:5]:
                    content += f"- {insight.description}\n"
                content += "\n"
            
            if report.recommendations:
                content += "## 💡 Recommendations\n"
                for rec in report.recommendations:
                    content += f"- {rec}\n"
                content += "\n"
            
            content += f"---\n*Report generated at {report.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}*"
            
            return content
            
        except Exception as e:
            logger.error(f"Error formatting generic report for markdown: {e}")
            return f"# Error\n\nUnable to format report: {str(e)}"
    
    def _format_daily_summary_email(self, report: GeneratedReport) -> str:
        """Format daily summary report for email (HTML)."""
        try:
            # Convert markdown to HTML
            markdown_content = self._format_daily_summary_markdown(report)
            
            # Simple markdown to HTML conversion
            html_content = markdown_content.replace("# ", "<h1>").replace("\n", "</h1>\n")
            html_content = html_content.replace("## ", "<h2>").replace("\n", "</h2>\n")
            html_content = html_content.replace("- ", "<li>").replace("\n", "</li>\n")
            html_content = html_content.replace("---", "<hr>")
            
            # Wrap in HTML structure
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>{report.title}</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #2c3e50; }}
                    h2 {{ color: #34495e; margin-top: 20px; }}
                    li {{ margin: 5px 0; }}
                    hr {{ border: 1px solid #ecf0f1; margin: 20px 0; }}
                </style>
            </head>
            <body>
                {html_content}
            </body>
            </html>
            """
            
            return html
            
        except Exception as e:
            logger.error(f"Error formatting daily summary for email: {e}")
            return f"<h1>Error</h1><p>Unable to format daily summary report: {str(e)}</p>"
    
    def _format_health_dashboard_email(self, report: GeneratedReport) -> str:
        """Format health dashboard report for email (HTML)."""
        try:
            markdown_content = self._format_health_dashboard_markdown(report)
            
            # Convert to HTML (same logic as daily summary)
            html_content = markdown_content.replace("# ", "<h1>").replace("\n", "</h1>\n")
            html_content = html_content.replace("## ", "<h2>").replace("\n", "</h2>\n")
            html_content = html_content.replace("- ", "<li>").replace("\n", "</li>\n")
            html_content = html_content.replace("---", "<hr>")
            
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>{report.title}</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #2c3e50; }}
                    h2 {{ color: #34495e; margin-top: 20px; }}
                    li {{ margin: 5px 0; }}
                    hr {{ border: 1px solid #ecf0f1; margin: 20px 0; }}
                </style>
            </head>
            <body>
                {html_content}
            </body>
            </html>
            """
            
            return html
            
        except Exception as e:
            logger.error(f"Error formatting health dashboard for email: {e}")
            return f"<h1>Error</h1><p>Unable to format health dashboard report: {str(e)}</p>"
    
    def _format_anomaly_alert_email(self, report: GeneratedReport) -> str:
        """Format anomaly alert report for email (HTML)."""
        try:
            markdown_content = self._format_anomaly_alert_markdown(report)
            
            # Convert to HTML
            html_content = markdown_content.replace("# ", "<h1>").replace("\n", "</h1>\n")
            html_content = html_content.replace("## ", "<h2>").replace("\n", "</h2>\n")
            html_content = html_content.replace("- ", "<li>").replace("\n", "</li>\n")
            html_content = html_content.replace("---", "<hr>")
            
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>Anomaly Alert</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #e74c3c; }}
                    h2 {{ color: #c0392b; margin-top: 20px; }}
                    li {{ margin: 5px 0; }}
                    hr {{ border: 1px solid #ecf0f1; margin: 20px 0; }}
                </style>
            </head>
            <body>
                {html_content}
            </body>
            </html>
            """
            
            return html
            
        except Exception as e:
            logger.error(f"Error formatting anomaly alert for email: {e}")
            return f"<h1>Error</h1><p>Unable to format anomaly alert: {str(e)}</p>"
    
    def _format_generic_email(self, report: GeneratedReport) -> str:
        """Format generic report for email (HTML)."""
        try:
            markdown_content = self._format_generic_markdown(report)
            
            # Convert to HTML
            html_content = markdown_content.replace("# ", "<h1>").replace("\n", "</h1>\n")
            html_content = html_content.replace("## ", "<h2>").replace("\n", "</h2>\n")
            html_content = html_content.replace("- ", "<li>").replace("\n", "</li>\n")
            html_content = html_content.replace("---", "<hr>")
            
            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>{report.title}</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h1 {{ color: #2c3e50; }}
                    h2 {{ color: #34495e; margin-top: 20px; }}
                    li {{ margin: 5px 0; }}
                    hr {{ border: 1px solid #ecf0f1; margin: 20px 0; }}
                </style>
            </head>
            <body>
                {html_content}
            </body>
            </html>
            """
            
            return html
            
        except Exception as e:
            logger.error(f"Error formatting generic report for email: {e}")
            return f"<h1>Error</h1><p>Unable to format report: {str(e)}</p>"
    
    def _format_error_discord(self, report: GeneratedReport, error: Exception) -> str:
        """Format error message for Discord."""
        return f"❌ **Error Generating Report**\n\nUnable to format {report.report_type.value} report: {str(error)}\n\n*Please check system logs for details*"
    
    def _format_error_markdown(self, report: GeneratedReport, error: Exception) -> str:
        """Format error message for markdown."""
        return f"# Error\n\nUnable to format {report.report_type.value} report: {str(error)}\n\n*Please check system logs for details*"
    
    def _format_error_email(self, report: GeneratedReport, error: Exception) -> str:
        """Format error message for email."""
        return f"<h1>Error</h1><p>Unable to format {report.report_type.value} report: {str(error)}</p><p><em>Please check system logs for details</em></p>"
    
    def _format_error_json(self, report: GeneratedReport, error: Exception) -> str:
        """Format error message for JSON."""
        import json
        error_data = {
            "error": True,
            "message": f"Unable to format {report.report_type.value} report",
            "error_details": str(error),
            "timestamp": datetime.now().isoformat()
        }
        return json.dumps(error_data, indent=2)


# Global formatter instance
report_formatter = ReportFormatter()


def format_report_for_discord(report: GeneratedReport) -> FormattedReport:
    """Convenience function to format report for Discord."""
    return report_formatter.format_for_discord(report)


def format_report_for_markdown(report: GeneratedReport) -> FormattedReport:
    """Convenience function to format report for markdown."""
    return report_formatter.format_for_markdown(report)


def format_report_for_email(report: GeneratedReport) -> FormattedReport:
    """Convenience function to format report for email."""
    return report_formatter.format_for_email(report)


def format_report_for_json(report: GeneratedReport) -> FormattedReport:
    """Convenience function to format report for JSON."""
    return report_formatter.format_for_json(report) 