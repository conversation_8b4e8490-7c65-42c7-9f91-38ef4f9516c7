"""
ASK Pipeline Executor

Discord integration layer for the new simplified ASK pipeline.
Handles Discord interactions and provides a clean interface to the pipeline.
"""

import asyncio
from typing import Optional, Union
import discord

from src.shared.error_handling.logging import get_logger, generate_correlation_id
from .pipeline import AskPipeline, PipelineResult
from .config import get_config

logger = get_logger(__name__)

async def execute_ask_pipeline(
    query: str,
    user_id: Optional[str] = None,
    username: Optional[str] = None,
    guild_id: Optional[str] = None,
    correlation_id: Optional[str] = None,
    interaction: Optional[discord.Interaction] = None,
    **kwargs  # For backward compatibility with old parameters
) -> PipelineResult:
    """
    Execute the ASK pipeline with Discord integration
    
    This function provides backward compatibility with the old executor interface
    while using the new simplified pipeline architecture.
    
    Args:
        query: User's question or request
        user_id: Discord user ID
        username: Discord username
        guild_id: Discord guild ID
        correlation_id: Request correlation ID
        interaction: Discord interaction object
        **kwargs: Additional parameters for backward compatibility
        
    Returns:
        PipelineResult with response and metadata
    """
    correlation_id = correlation_id or generate_correlation_id()
    config = get_config()
    
    logger.info(f"Executing ASK pipeline", extra={
        'correlation_id': correlation_id,
        'user_id': user_id,
        'username': username,
        'guild_id': guild_id,
        'query_length': len(query)
    })
    
    try:
        # Initialize the pipeline
        pipeline = AskPipeline()
        
        # Process the query
        result = await pipeline.process(
            query=query,
            user_id=user_id,
            correlation_id=correlation_id
        )
        
        # Log the result
        logger.info(f"ASK pipeline execution completed", extra={
            'correlation_id': correlation_id,
            'success': result.success,
            'execution_time': result.execution_time,
            'intent': result.intent,
            'tools_used': result.tools_used,
            'cache_hit': result.cache_hit
        })
        
        return result
        
    except Exception as e:
        logger.error(f"ASK pipeline execution failed", extra={
            'correlation_id': correlation_id,
            'error': str(e)
        }, exc_info=True)
        
        # Return error result
        return PipelineResult(
            success=False,
            response=f"An error occurred while processing your request. Please try again.",
            error=str(e),
            correlation_id=correlation_id
        )

def format_response_for_discord(result: PipelineResult) -> Union[str, discord.Embed]:
    """
    Format pipeline result for Discord response
    
    Args:
        result: Pipeline execution result
        
    Returns:
        Formatted response (string or embed)
    """
    if result.embed:
        # If we have an embed, use it
        embed = discord.Embed.from_dict(result.embed)
        return embed
    elif result.response:
        # Otherwise use text response
        return result.response
    else:
        # Fallback error message
        return "Sorry, I couldn't generate a response. Please try again."

# Backward compatibility function names
async def execute_ask_command(*args, **kwargs):
    """Backward compatibility alias"""
    return await execute_ask_pipeline(*args, **kwargs)

# For compatibility with the old system
class PipelineContext:
    """Compatibility class for old pipeline context"""
    
    def __init__(self, result: PipelineResult):
        self.result = result
        self.success = result.success
        self.response = result.response
        self.error = result.error
        self.execution_time = result.execution_time
        self.correlation_id = result.correlation_id
        
    def get_response(self) -> str:
        """Get the response text"""
        return self.response or "No response generated"
    
    def get_embed(self) -> Optional[dict]:
        """Get the embed data"""
        return self.result.embed
    
    def is_success(self) -> bool:
        """Check if pipeline was successful"""
        return self.success
