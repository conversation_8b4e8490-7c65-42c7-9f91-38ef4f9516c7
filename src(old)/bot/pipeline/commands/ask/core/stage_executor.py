"""
Stage Executor

Handles the execution of individual pipeline stages with proper error handling,
timeout management, and audit logging.

Design Principles:
- Single responsibility: Execute individual stages
- Error delegation: Pass errors to error coordinator
- Audit integration: Comprehensive logging
- Timeout handling: Proper async timeout management
"""

import asyncio
from typing import Optional, Any

from src.shared.error_handling.logging import get_logger
from .error_coordinator import ErrorCoordinator
from ..cache import CacheManager
from ..config import ConfigurationManager

logger = get_logger(__name__)

class StageExecutor:
    """
    Executes individual pipeline stages with proper error handling
    
    Handles:
    - Intent Detection
    - Tool Orchestration
    - Response Generation
    - Discord Formatting
    """

    def __init__(self,
                 config: ConfigurationManager,
                 cache_manager: CacheManager,
                 error_coordinator: ErrorCoordinator):
        """
        Initialize stage executor with dependencies
        
        Args:
            config: Pipeline configuration manager
            cache_manager: Cache management component
            error_coordinator: Error handling component
        """
        self.config = config
        self.cache_manager = cache_manager
        self.error_coordinator = error_coordinator
        
        # Import stage components
        self._import_stage_components()

    def _import_stage_components(self):
        """Import all stage components lazily to avoid circular imports"""
        from ..stages.intent_detector import IntentDetector
        from ..stages.simplified_tool_orchestrator import SimplifiedToolOrchestrator
        from ..stages.response_generator import ResponseGenerator
        from ..stages.formatter import DiscordFormatter
        
        self.intent_detector = IntentDetector()
        self.tool_orchestrator = SimplifiedToolOrchestrator()
        self.response_generator = ResponseGenerator()
        self.formatter = DiscordFormatter()

    async def execute_intent_detection(
        self, 
        query: str, 
        correlation_id: str, 
        audit_logger: Optional[Any] = None
    ) -> Any:
        """Execute intent detection stage"""
        if audit_logger:
            audit_logger.start_step(
                step_id="intent_detection",
                step_name="Intent Detection",
                stage="intent_detection",
                input_data={"query": query}
            )
        
        try:
            # Check cache first
            cached_result = await self.cache_manager.get_intent_cache(query, correlation_id)
            if cached_result:
                logger.debug(f"Using cached intent result", extra={'correlation_id': correlation_id})
                if audit_logger:
                    audit_logger.complete_step(
                        status="completed",
                        output_data={"intent": cached_result.intent, "cache_hit": True},
                        reasoning="Using cached intent result"
                    )
                return cached_result

            # Execute intent detection
            intent_result = await asyncio.wait_for(
                self.intent_detector.detect(query, correlation_id),
                timeout=self.config.intent_detection.timeout
            )
            
            # Cache the result
            await self.cache_manager.set_intent_cache(query, intent_result, correlation_id)
            
            if audit_logger:
                audit_logger.log_decision(
                    decision_point="intent_detection_result",
                    options=["casual", "data_needed"],
                    chosen_option=intent_result.intent,
                    reasoning=f"Intent detected as {intent_result.intent} with confidence {intent_result.confidence}",
                    confidence=intent_result.confidence,
                    context={
                        "intent": intent_result.intent,
                        "confidence": intent_result.confidence,
                        "reasoning": getattr(intent_result, 'reasoning', 'N/A')
                    }
                )
                audit_logger.complete_step(
                    status="completed",
                    output_data={
                        "intent": intent_result.intent,
                        "confidence": intent_result.confidence,
                        "reasoning": getattr(intent_result, 'reasoning', 'N/A')
                    },
                    reasoning=f"Intent detection completed: {intent_result.intent}"
                )
            
            return intent_result

        except asyncio.TimeoutError:
            logger.warning(f"Intent detection timed out", extra={'correlation_id': correlation_id})
            if audit_logger:
                audit_logger.complete_step(
                    status="timeout",
                    output_data={"error": "timeout"},
                    reasoning="Intent detection timed out"
                )
            # Use error coordinator for timeout
            return await self.error_coordinator.handle_stage_timeout(
                stage="intent_detection",
                correlation_id=correlation_id,
                timeout_seconds=self.config.intent_detection.timeout
            )
        except Exception as e:
            logger.error(f"Intent detection failed", extra={'correlation_id': correlation_id, 'error': str(e)})
            if audit_logger:
                audit_logger.complete_step(
                    status="failed",
                    output_data={"error": str(e)},
                    reasoning="Intent detection failed"
                )
            # Use error coordinator
            return await self.error_coordinator.handle_stage_error(
                stage="intent_detection",
                exception=e,
                correlation_id=correlation_id
            )

    async def execute_tool_orchestration(
        self,
        query: str,
        intent_result: Any,
        correlation_id: str,
        audit_logger: Optional[Any] = None
    ) -> Optional[Any]:
        """Execute tool orchestration stage"""
        if intent_result.intent == "casual":
            logger.debug(f"Skipping tools for casual query", extra={'correlation_id': correlation_id})
            if audit_logger:
                audit_logger.start_step(
                    step_id="tool_orchestration",
                    step_name="Tool Orchestration",
                    stage="tool_orchestration",
                    input_data={"query": query, "intent": intent_result.intent}
                )
                audit_logger.log_decision(
                    decision_point="tool_execution_strategy",
                    options=["execute_tools", "skip_tools"],
                    chosen_option="skip_tools",
                    reasoning=f"Intent is {intent_result.intent}, no tools needed",
                    confidence=1.0
                )
                audit_logger.complete_step(
                    status="completed",
                    output_data={"tools_used": [], "cache_hit": False},
                    reasoning="No tools needed for casual query"
                )
            return None

        if audit_logger:
            audit_logger.start_step(
                step_id="tool_orchestration",
                step_name="Tool Orchestration",
                stage="tool_orchestration",
                input_data={
                    "query": query,
                    "intent": intent_result.intent,
                    "intent_confidence": intent_result.confidence
                }
            )

        try:
            # Check cache first
            cached_result = await self.cache_manager.get_tool_cache(query, intent_result, correlation_id)
            if cached_result:
                logger.debug(f"Using cached tool result", extra={'correlation_id': correlation_id})
                cached_result.cache_hit = True
                if audit_logger:
                    audit_logger.complete_step(
                        status="completed",
                        output_data={"tools_used": cached_result.tools_used, "cache_hit": True},
                        reasoning="Using cached tool result"
                    )
                return cached_result

            # Execute tools
            tool_result = await asyncio.wait_for(
                self.tool_orchestrator.execute(query, intent_result, correlation_id),
                timeout=self.config.tools.timeout
            )

            # Cache the result if successful
            if tool_result:
                await self.cache_manager.set_tool_cache(query, intent_result, tool_result, correlation_id)

            if audit_logger:
                if hasattr(tool_result, 'tools_used'):
                    audit_logger.log_decision(
                        decision_point="dynamic_tool_execution_strategy",
                        options=["execute_tools", "skip_tools"],
                        chosen_option="execute_tools",
                        reasoning=f"Dynamic orchestration executed {tool_result.iterations} iterations with satisfaction {tool_result.satisfaction_score.overall_score:.2f}",
                        confidence=tool_result.confidence,
                        context={
                            "tools_used": tool_result.tools_used,
                            "iterations": tool_result.iterations,
                            "satisfaction_score": tool_result.satisfaction_score.overall_score,
                            "knowledge_gaps_addressed": tool_result.knowledge_gaps_addressed,
                            "total_duration_ms": tool_result.total_duration_ms
                        }
                    )
                    audit_logger.complete_step(
                        status="completed",
                        output_data={
                            "tools_used": tool_result.tools_used,
                            "iterations": tool_result.iterations,
                            "satisfaction_score": tool_result.satisfaction_score.overall_score,
                            "knowledge_gaps_addressed": tool_result.knowledge_gaps_addressed,
                            "total_duration_ms": tool_result.total_duration_ms,
                            "final_answer_length": len(tool_result.final_answer)
                        },
                        reasoning=f"Dynamic orchestration completed: {tool_result.tools_used} tools in {tool_result.iterations} iterations"
                    )
                else:
                    audit_logger.log_decision(
                        decision_point="tool_execution_strategy",
                        options=["execute_tools", "skip_tools"],
                        chosen_option="execute_tools",
                        reasoning=f"Intent is {intent_result.intent}, tools needed for data gathering",
                        confidence=1.0,
                        context={
                            "tools_used": tool_result.tools_used,
                            "cache_hit": tool_result.cache_hit,
                            "execution_time": getattr(tool_result, 'execution_time', 0)
                        }
                    )
                    audit_logger.complete_step(
                        status="completed",
                        output_data={
                            "tools_used": tool_result.tools_used,
                            "cache_hit": tool_result.cache_hit,
                            "execution_time": getattr(tool_result, 'execution_time', 0)
                        },
                        reasoning=f"Tools executed successfully: {tool_result.tools_used}"
                    )

            return tool_result

        except asyncio.TimeoutError:
            logger.warning(f"Tool orchestration timed out", extra={'correlation_id': correlation_id})
            if audit_logger:
                audit_logger.complete_step(
                    status="timeout",
                    output_data={"error": "timeout"},
                    reasoning="Tool orchestration timed out"
                )
            return await self.error_coordinator.handle_stage_timeout(
                stage="tool_orchestration",
                correlation_id=correlation_id,
                timeout_seconds=self.config.tools.timeout
            )
        except Exception as e:
            logger.error(f"Tool orchestration failed", extra={'correlation_id': correlation_id, 'error': str(e)})
            if audit_logger:
                audit_logger.complete_step(
                    status="failed",
                    output_data={"error": str(e)},
                    reasoning="Tool orchestration failed"
                )
            return await self.error_coordinator.handle_stage_error(
                stage="tool_orchestration",
                exception=e,
                correlation_id=correlation_id
            )

    async def execute_response_generation(
        self,
        query: str,
        intent_result: Any,
        tool_result: Optional[Any],
        correlation_id: str,
        audit_logger: Optional[Any] = None
    ) -> Any:
        """Execute response generation stage"""
        if audit_logger:
            audit_logger.start_step(
                step_id="response_generation",
                step_name="Response Generation",
                stage="response_generation",
                input_data={
                    "query": query,
                    "intent": intent_result.intent,
                    "tool_result_available": tool_result is not None
                }
            )

        try:
            # Handle dynamic tool result - if it has final_answer, use it directly
            if tool_result and hasattr(tool_result, 'final_answer'):
                # Dynamic orchestrator already generated the final answer
                from ..stages.response_generator import ResponseResult
                response_result = ResponseResult(
                    response=tool_result.final_answer,
                    confidence=tool_result.confidence,
                    execution_time=tool_result.total_duration_ms / 1000.0
                )
                # Add metadata as attributes
                response_result.sources = tool_result.tools_used
                response_result.metadata = {
                    'satisfaction_score': tool_result.satisfaction_score.overall_score,
                    'iterations': tool_result.iterations,
                    'knowledge_gaps_addressed': tool_result.knowledge_gaps_addressed,
                    'dynamic_orchestration': True
                }
            else:
                # Fallback to traditional response generation
                response_result = await asyncio.wait_for(
                    self.response_generator.generate(query, intent_result, tool_result, correlation_id),
                    timeout=self.config.response.timeout
                )

            if audit_logger:
                audit_logger.log_decision(
                    decision_point="response_generation_strategy",
                    options=["ai_generated", "cached", "fallback"],
                    chosen_option="ai_generated",
                    reasoning="Generated AI response using intent and tool data",
                    confidence=1.0,
                    context={
                        "response_length": len(str(response_result)) if hasattr(response_result, '__len__') else 0,
                        "success": getattr(response_result, 'success', True)
                    }
                )
                audit_logger.complete_step(
                    status="completed",
                    output_data={
                        "response_generated": True,
                        "response_length": len(str(response_result)) if hasattr(response_result, '__len__') else 0
                    },
                    reasoning="AI response generated successfully"
                )

            return response_result

        except asyncio.TimeoutError:
            logger.warning(f"Response generation timed out", extra={'correlation_id': correlation_id})
            if audit_logger:
                audit_logger.complete_step(
                    status="timeout",
                    output_data={"error": "timeout"},
                    reasoning="Response generation timed out"
                )
            return await self.error_coordinator.handle_stage_timeout(
                stage="response_generation",
                correlation_id=correlation_id,
                timeout_seconds=self.config.response.timeout
            )
        except Exception as e:
            logger.error(f"Response generation failed", extra={'correlation_id': correlation_id, 'error': str(e)})
            if audit_logger:
                audit_logger.complete_step(
                    status="failed",
                    output_data={"error": str(e)},
                    reasoning="Response generation failed"
                )
            return await self.error_coordinator.handle_stage_error(
                stage="response_generation",
                exception=e,
                correlation_id=correlation_id
            )

    async def execute_discord_formatting(
        self,
        response_result: Any,
        correlation_id: str,
        audit_logger: Optional[Any] = None
    ) -> Any:
        """Execute Discord formatting stage"""
        if audit_logger:
            audit_logger.start_step(
                step_id="discord_formatting",
                step_name="Discord Formatting",
                stage="discord_formatting",
                input_data={"response_result": str(response_result)[:100] + "..." if len(str(response_result)) > 100 else str(response_result)}
            )

        try:
            logger.info(f"🔍 Response result before formatting: {response_result.response[:200]}...", extra={'correlation_id': correlation_id})
            formatted_response = await asyncio.wait_for(
                self.formatter.format(response_result, correlation_id),
                timeout=1.0  # Formatting should be very fast
            )
            logger.info(f"🔍 Formatted response: {formatted_response.text[:200] if formatted_response.text else 'None'}...", extra={'correlation_id': correlation_id})

            if audit_logger:
                response_type = "embed" if hasattr(formatted_response, 'embed') and formatted_response.embed else "text"
                audit_logger.log_decision(
                    decision_point="response_format_type",
                    options=["embed", "text"],
                    chosen_option=response_type,
                    reasoning=f"Response formatted as {response_type} for Discord",
                    confidence=1.0,
                    context={"response_type": response_type}
                )
                audit_logger.complete_step(
                    status="completed",
                    output_data={
                        "formatted": True,
                        "response_type": response_type,
                        "text_length": len(getattr(formatted_response, "text", "") or "")
                    },
                    reasoning=f"Response formatted as {response_type} for Discord"
                )

            return formatted_response

        except asyncio.TimeoutError:
            logger.warning(f"Response formatting timed out", extra={'correlation_id': correlation_id})
            if audit_logger:
                audit_logger.complete_step(
                    status="timeout",
                    output_data={"error": "timeout"},
                    reasoning="Response formatting timed out"
                )
            return await self.error_coordinator.handle_stage_timeout(
                stage="response_formatting",
                correlation_id=correlation_id,
                timeout_seconds=1.0
            )
        except Exception as e:
            logger.error(f"Response formatting failed", extra={'correlation_id': correlation_id, 'error': str(e)})
            if audit_logger:
                audit_logger.complete_step(
                    status="failed",
                    output_data={"error": str(e)},
                    reasoning="Response formatting failed"
                )
            return await self.error_coordinator.handle_stage_error(
                stage="response_formatting",
                exception=e,
                correlation_id=correlation_id
            )
