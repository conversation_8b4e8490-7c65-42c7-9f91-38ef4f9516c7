"""
ASK Pipeline Controller

Main orchestrator for the ASK pipeline that coordinates all stages and components.
This replaces the monolithic pipeline.py with a clean, focused controller.

Design Principles:
- Single responsibility: Orchestrate pipeline execution
- Dependency injection: All components injected via constructor
- Clean interfaces: Well-defined contracts between components
- Error coordination: Delegates error handling to error coordinator
- Stage management: Delegates stage coordination to stage manager
"""

import asyncio
import time
from typing import Dict, Any, Optional, TYPE_CHECKING
from dataclasses import dataclass

from src.shared.error_handling.logging import get_logger, generate_correlation_id
from .stage_manager import StageManager
from .error_coordinator import ErrorCoordinator
from ..config import get_config
from ..cache import CacheConfig
from ..observability import (
    get_structured_logger,
    get_metrics_collector,
    get_health_checker,
    get_tracer,
    LogContext,
    LogCategory,
    SpanKind,
    set_correlation_id,
    clear_correlation_id
)

if TYPE_CHECKING:
    from ..cache import UnifiedCacheManager
from ..audit import get_audit_logger

logger = get_logger(__name__)

@dataclass
class PipelineResult:
    """Result of pipeline execution"""
    success: bool
    response: Optional[str] = None
    embed: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    execution_time: float = 0.0
    correlation_id: str = ""
    intent: Optional[str] = None
    tools_used: list = None
    cache_hit: bool = False
    confidence: float = 0.0

class AskPipelineController:
    """
    Main ASK pipeline controller
    
    Orchestrates the entire pipeline flow by delegating to specialized components:
    - StageManager: Coordinates stage execution
    - ErrorCoordinator: Handles all error scenarios
    - CacheManager: Manages caching strategy
    """

    def __init__(self, 
                 stage_manager: Optional[StageManager] = None,
                 error_coordinator: Optional[ErrorCoordinator] = None,
                 cache_manager: Optional['UnifiedCacheManager'] = None):
        """
        Initialize the pipeline controller with dependency injection
        
        Args:
            stage_manager: Stage coordination component
            error_coordinator: Error handling component  
            cache_manager: Caching component
        """
        self.config = get_config()

        # Initialize observability components
        self.structured_logger = get_structured_logger(f"{__name__}.controller")
        self.metrics_collector = get_metrics_collector()
        self.health_checker = get_health_checker()
        self.tracer = get_tracer()

        # Initialize components with dependency injection (order matters)
        self.cache_manager = cache_manager or self._create_cache_manager()
        self.error_coordinator = error_coordinator or self._create_error_coordinator()
        self.stage_manager = stage_manager or self._create_stage_manager()

    def _create_stage_manager(self) -> StageManager:
        """Create and configure stage manager"""
        return StageManager(
            config=self.config,
            cache_manager=self.cache_manager,
            error_coordinator=self.error_coordinator
        )

    def _create_error_coordinator(self) -> ErrorCoordinator:
        """Create and configure error coordinator"""
        return ErrorCoordinator(config=self.config)

    def _create_cache_manager(self) -> 'UnifiedCacheManager':
        """Create and configure unified cache manager"""
        from ..cache import UnifiedCacheConfig, UnifiedCacheManager

        cache_config = UnifiedCacheConfig(
            enabled=True,
            use_redis=True,
            fallback_to_memory=True,
            max_memory_mb=50,
            default_ttl=300,
            max_entries=500,
            cache_intent=True,
            cache_tools=True,
            cache_responses=True,
            async_cache_writes=True,
            intelligent_ttl=True,
            cache_analytics=True
        )
        return UnifiedCacheManager(cache_config)

    async def process(
        self,
        query: str,
        user_id: Optional[str] = None,
        correlation_id: Optional[str] = None
    ) -> PipelineResult:
        """
        Process a user query through the complete pipeline
        
        Args:
            query: User's question or request
            user_id: Discord user ID for context
            correlation_id: Request correlation ID
            
        Returns:
            PipelineResult with response and metadata
        """
        start_time = time.time()
        correlation_id = correlation_id or generate_correlation_id()

        # Set correlation ID for observability tracking
        set_correlation_id(correlation_id)

        # Start distributed trace
        trace_context = self.tracer.start_trace(
            operation_name="ask_pipeline_process",
            trace_id=correlation_id
        )

        # Create log context
        log_context = LogContext(
            correlation_id=correlation_id,
            user_id=user_id,
            stage="pipeline",
            component="controller",
            operation="process"
        )

        # Record request metrics
        self.metrics_collector.record_request("ask", "started")

        # Log request start
        self.structured_logger.info(
            f"Starting ASK pipeline processing",
            context=log_context,
            category=LogCategory.PIPELINE,
            extra_data={
                "query_length": len(query),
                "user_id": user_id[:8] if user_id else None  # Truncated for privacy
            }
        )
        
        # Get audit logger if available
        audit_logger = get_audit_logger()

        # Legacy logger for compatibility
        logger.info(f"Starting ASK pipeline", extra={
            'correlation_id': correlation_id,
            'query_length': len(query),
            'user_id': user_id
        })

        # AUDIT: Log pipeline start
        if audit_logger:
            audit_logger.start_step(
                step_id="pipeline_process_start",
                step_name="ASK Pipeline Process",
                stage="pipeline_main",
                input_data={
                    "query": query,
                    "user_id": user_id,
                    "correlation_id": correlation_id
                }
            )
            
            audit_logger.log_plan(
                plan_name="ASK Pipeline Execution",
                strategy="Execute 4-stage pipeline: Intent Detection → Tool Orchestration → Response Generation → Discord Formatting",
                steps=[
                    "intent_detection",
                    "tool_orchestration", 
                    "response_generation",
                    "discord_formatting"
                ],
                expected_duration=15.0,
                fallback_strategy="Use error coordinator for any stage failures",
                success_criteria=[
                    "Intent detected successfully",
                    "Tools executed (if needed)",
                    "Response generated",
                    "Response formatted for Discord"
                ]
            )

        try:
            # Add trace tags
            self.tracer.set_span_tag(trace_context, "query.length", len(query))
            self.tracer.set_span_tag(trace_context, "user.id", user_id[:8] if user_id else "anonymous")
            self.tracer.add_span_event(trace_context, "pipeline.started")

            # Delegate to stage manager for execution
            result = await self.stage_manager.execute_pipeline(
                query=query,
                user_id=user_id,
                correlation_id=correlation_id,
                audit_logger=audit_logger
            )

            execution_time = time.time() - start_time
            result.execution_time = execution_time
            result.correlation_id = correlation_id

            # Add trace completion tags
            self.tracer.set_span_tag(trace_context, "pipeline.success", True)
            self.tracer.set_span_tag(trace_context, "pipeline.execution_time", execution_time)
            self.tracer.set_span_tag(trace_context, "pipeline.intent", result.intent)
            self.tracer.set_span_tag(trace_context, "pipeline.cache_hit", result.cache_hit)
            self.tracer.add_span_event(trace_context, "pipeline.completed")

            # Record successful completion metrics
            self.metrics_collector.record_request("ask", "success")
            self.metrics_collector.record_request_duration(execution_time, "pipeline", True)

            # Log successful completion
            self.structured_logger.info(
                f"ASK pipeline completed successfully",
                context=log_context,
                category=LogCategory.PIPELINE,
                extra_data={
                    "execution_time": execution_time,
                    "intent": result.intent,
                    "tools_used": result.tools_used or [],
                    "cache_hit": result.cache_hit,
                    "confidence": result.confidence
                }
            )

            # Audit log successful completion
            self.structured_logger.audit(
                action="pipeline_completed",
                user_id=user_id,
                query=query,
                success=True,
                context=log_context,
                execution_time=execution_time,
                tools_used=result.tools_used or []
            )

            # Legacy logger for compatibility
            logger.info(f"ASK pipeline completed successfully", extra={
                'correlation_id': correlation_id,
                'execution_time': execution_time,
                'intent': result.intent,
                'tools_used': result.tools_used or [],
                'cache_hit': result.cache_hit
            })

            # AUDIT: Complete pipeline
            if audit_logger:
                audit_logger.complete_step(
                    status="completed",
                    output_data={
                        "success": True,
                        "execution_time": execution_time,
                        "intent": result.intent,
                        "tools_used": result.tools_used or [],
                        "cache_hit": result.cache_hit
                    },
                    reasoning="ASK pipeline completed successfully"
                )

            return result

        except Exception as e:
            execution_time = time.time() - start_time

            # Add error to trace
            self.tracer.set_span_error(trace_context, e)
            self.tracer.set_span_tag(trace_context, "pipeline.success", False)
            self.tracer.set_span_tag(trace_context, "pipeline.execution_time", execution_time)
            self.tracer.add_span_event(trace_context, "pipeline.failed", {
                "error_type": type(e).__name__,
                "error_message": str(e)
            })

            # Record error metrics
            self.metrics_collector.record_request("ask", "error")
            self.metrics_collector.record_request_duration(execution_time, "pipeline", False)

            # Log error with structured logging
            self.structured_logger.error(
                f"ASK pipeline failed: {str(e)}",
                context=log_context,
                category=LogCategory.ERROR,
                extra_data={
                    "execution_time": execution_time,
                    "error_type": type(e).__name__,
                    "error_message": str(e)
                }
            )

            # Audit log error
            self.structured_logger.audit(
                action="pipeline_failed",
                user_id=user_id,
                query=query,
                success=False,
                context=log_context,
                execution_time=execution_time,
                error_type=type(e).__name__
            )

            # Legacy logger for compatibility
            logger.error(f"ASK pipeline failed", extra={
                'correlation_id': correlation_id,
                'execution_time': execution_time,
                'error': str(e)
            }, exc_info=True)

            # Delegate error handling to error coordinator
            return await self.error_coordinator.handle_pipeline_error(
                exception=e,
                query=query,
                user_id=user_id,
                correlation_id=correlation_id,
                execution_time=execution_time,
                audit_logger=audit_logger
            )
        finally:
            # Finish the trace
            self.tracer.finish_span(trace_context)

            # Clear correlation ID from context
            clear_correlation_id()
