"""
Error Coordinator

Centralized error handling and coordination for the ASK pipeline.
Handles all error scenarios, fallback strategies, and user messaging.

Design Principles:
- Single responsibility: Error handling only
- Comprehensive coverage: All error scenarios handled
- User-friendly: Clear error messages for users
- Audit integration: Proper error logging and tracking
- Fallback strategies: Graceful degradation when possible
"""

import time
from typing import Optional, Any, Dict


from src.shared.error_handling.logging import get_logger
from ..errors import ErrorManager, ErrorContext, ErrorSeverity, FallbackStrategy, ErrorResult
from ..config import get_config_manager, ConfigurationManager

logger = get_logger(__name__)

# Using centralized ErrorResult from ..errors

class ErrorCoordinator:
    """
    Coordinates error handling across the entire pipeline
    
    Provides centralized error handling for:
    - Pipeline-level errors
    - Stage-specific errors
    - Timeout scenarios
    - Fallback strategies
    """

    def __init__(self, config: ConfigurationManager):
        """
        Initialize error coordinator
        
        Args:
            config: Pipeline configuration manager
        """
        self.config = config
        self.error_manager = ErrorManager()
        self.fallback_strategy = FallbackStrategy()

    async def handle_pipeline_error(
        self,
        exception: Exception,
        query: str,
        user_id: Optional[str],
        correlation_id: str,
        execution_time: float,
        audit_logger: Optional[Any] = None
    ) -> 'PipelineResult':
        """
        Handle pipeline-level errors
        
        Args:
            exception: The exception that occurred
            query: Original user query
            user_id: Discord user ID
            correlation_id: Request correlation ID
            execution_time: Time spent before error
            audit_logger: Audit logging component
            
        Returns:
            PipelineResult with error response
        """
        logger.error(f"Pipeline error occurred", extra={
            'correlation_id': correlation_id,
            'error': str(exception),
            'execution_time': execution_time
        }, exc_info=True)

        # Use consolidated error handling
        try:
            # Create error context
            error_context = ErrorContext(
                stage="pipeline",
                user_id=user_id,
                query=query,
                retry_count=0,
                execution_time=execution_time
            )

            # Handle the error
            error_result = await self.error_manager.handle_error(
                exception=exception,
                context=error_context,
                correlation_id=correlation_id
            )

            # Convert error result to pipeline result
            response_result = self._error_result_to_pipeline_result(error_result)
            
            # Format the response
            from ..stages.formatter import DiscordFormatter
            formatter = DiscordFormatter()
            formatted_response = await formatter.format(response_result, correlation_id)

            # AUDIT: Log error handling
            if audit_logger:
                audit_logger.log_decision(
                    decision_point="pipeline_error_handling",
                    options=["error_handler", "emergency_fallback"],
                    chosen_option="error_handler",
                    reasoning=f"Used error handler for {type(exception).__name__}",
                    confidence=1.0,
                    context={
                        "error_type": type(exception).__name__,
                        "error_message": str(exception),
                        "execution_time": execution_time
                    }
                )
                audit_logger.complete_step(
                    status="completed",
                    output_data={
                        "error_handled": True,
                        "error_type": error_result.error_type,
                        "response_generated": True
                    },
                    reasoning="Pipeline error handled successfully"
                )

            from .controller import PipelineResult
            return PipelineResult(
                success=error_result.success,
                response=formatted_response.text or "Error occurred",
                embed=formatted_response.embed,
                error=error_result.error_type,
                execution_time=execution_time,
                correlation_id=correlation_id
            )

        except Exception as handler_error:
            # Emergency fallback if error handler fails
            logger.critical(f"Error handler failed", extra={
                'correlation_id': correlation_id,
                'original_error': str(exception),
                'handler_error': str(handler_error)
            })

            # AUDIT: Log emergency fallback
            if audit_logger:
                audit_logger.log_decision(
                    decision_point="pipeline_error_handling",
                    options=["error_handler", "emergency_fallback"],
                    chosen_option="emergency_fallback",
                    reasoning="Error handler failed, using emergency fallback",
                    confidence=0.5,
                    context={
                        "original_error": str(exception),
                        "handler_error": str(handler_error)
                    }
                )
                audit_logger.complete_step(
                    status="completed",
                    output_data={
                        "error_handled": False,
                        "emergency_fallback_used": True,
                        "response_generated": True
                    },
                    reasoning="Emergency fallback used due to error handler failure"
                )

            from .controller import PipelineResult
            return PipelineResult(
                success=False,
                response="I'm experiencing technical difficulties. Please try again later.",
                error="critical_system_error",
                execution_time=execution_time,
                correlation_id=correlation_id
            )

    async def handle_stage_error(
        self,
        stage: str,
        exception: Exception,
        correlation_id: str,
        user_query: str = "",
        execution_time: float = 0.0
    ) -> Any:
        """
        Handle stage-specific errors
        
        Args:
            stage: Name of the stage that failed
            exception: The exception that occurred
            correlation_id: Request correlation ID
            user_query: Original user query
            execution_time: Time spent before error
            
        Returns:
            Appropriate fallback result for the stage
        """
        logger.error(f"Stage error in {stage}", extra={
            'correlation_id': correlation_id,
            'stage': stage,
            'error': str(exception)
        }, exc_info=True)

        try:
            error_context = ErrorContext(
                stage=stage,
                user_id=None,
                query=user_query,
                retry_count=0,
                execution_time=execution_time
            )
            error_result = await self.error_manager.handle_error(
                exception=exception,
                context=error_context,
                correlation_id=correlation_id
            )
            return self._error_result_to_pipeline_result(error_result)

        except Exception as handler_error:
            logger.critical(f"Error handler failed for stage {stage}", extra={
                'correlation_id': correlation_id,
                'stage': stage,
                'original_error': str(exception),
                'handler_error': str(handler_error)
            })

            # Return stage-specific fallback
            return self._get_stage_fallback(stage, correlation_id)

    async def handle_stage_timeout(
        self,
        stage: str,
        correlation_id: str,
        timeout_seconds: float
    ) -> Any:
        """
        Handle stage timeout scenarios
        
        Args:
            stage: Name of the stage that timed out
            correlation_id: Request correlation ID
            timeout_seconds: Timeout duration
            
        Returns:
            Appropriate fallback result for the stage
        """
        logger.warning(f"Stage timeout in {stage}", extra={
            'correlation_id': correlation_id,
            'stage': stage,
            'timeout_seconds': timeout_seconds
        })

        # Create timeout exception for error handler
        import asyncio
        timeout_exception = asyncio.TimeoutError(f"Stage {stage} timed out after {timeout_seconds} seconds")

        try:
            error_context = ErrorContext(
                stage=stage,
                user_id=None,
                query="",
                retry_count=0,
                execution_time=timeout_seconds
            )
            error_result = await self.error_manager.handle_error(
                exception=timeout_exception,
                context=error_context,
                correlation_id=correlation_id
            )
            return self._error_result_to_pipeline_result(error_result)

        except Exception as handler_error:
            logger.critical(f"Error handler failed for timeout in stage {stage}", extra={
                'correlation_id': correlation_id,
                'stage': stage,
                'handler_error': str(handler_error)
            })

            # Return stage-specific fallback
            return self._get_stage_fallback(stage, correlation_id)

    def _get_stage_fallback(self, stage: str, correlation_id: str) -> Any:
        """
        Get stage-specific fallback result
        
        Args:
            stage: Name of the stage
            correlation_id: Request correlation ID
            
        Returns:
            Fallback result appropriate for the stage
        """
        if stage == "intent_detection":
            # Default to data needed for safety
            from ..stages.intent_detector import IntentResult
            return IntentResult(intent="data_needed", confidence=0.5, reasoning="error_fallback")
        
        elif stage == "tool_orchestration":
            # Return None to skip tools
            return None
        
        elif stage == "response_generation":
            # Return simple error response
            from ..stages.response_generator import ResponseResult
            return ResponseResult(
                response="I encountered an issue generating a response. Please try again.",
                confidence=0.0,
                execution_time=0.0
            )
        
        elif stage == "response_formatting":
            # Return simple text fallback
            from ..stages.formatter import FormattedResponse
            return FormattedResponse(
                text="I encountered an issue formatting the response. Please try again.",
                embed=None,
                execution_time=0.001
            )
        
        else:
            # Generic fallback
            logger.warning(f"Unknown stage for fallback: {stage}", extra={'correlation_id': correlation_id})
            return None

    async def handle_critical_error(
        self,
        exception: Exception,
        correlation_id: str,
        context: Dict[str, Any]
    ) -> ErrorResult:
        """
        Handle critical system errors that require immediate attention
        
        Args:
            exception: The critical exception
            correlation_id: Request correlation ID
            context: Additional context about the error
            
        Returns:
            ErrorResult with critical error response
        """
        logger.critical(f"Critical system error", extra={
            'correlation_id': correlation_id,
            'error': str(exception),
            'context': context
        }, exc_info=True)

        return ErrorResult(
            success=False,
            response="A critical system error has occurred. The development team has been notified.",
            error_type="critical_system_error",
            severity=ErrorSeverity.CRITICAL,
            execution_time=0.0,
            correlation_id=correlation_id,
            retry_recommended=False,
            fallback_used=None
        )

    async def handle_user_error(
        self,
        error_type: str,
        user_message: str,
        correlation_id: str,
        context: Optional[Dict[str, Any]] = None
    ) -> ErrorResult:
        """
        Handle user-facing errors with appropriate messaging
        
        Args:
            error_type: Type of error (validation, rate_limit, etc.)
            user_message: User-friendly error message
            correlation_id: Request correlation ID
            context: Additional context
            
        Returns:
            ErrorResult with user-friendly response
        """
        logger.info(f"User error: {error_type}", extra={
            'correlation_id': correlation_id,
            'error_type': error_type,
            'context': context or {}
        })

        return ErrorResult(
            success=False,
            response=user_message,
            error_type=error_type,
            severity=ErrorSeverity.LOW,
            execution_time=0.0,
            correlation_id=correlation_id,
            retry_recommended=False
        )

    def _error_result_to_pipeline_result(self, error_result) -> 'PipelineResult':
        """Convert ErrorResult to PipelineResult"""
        from .controller import PipelineResult

        return PipelineResult(
            success=False,
            response=error_result.response,
            confidence=0.1,
            execution_time=error_result.execution_time,
            metadata={
                'error_type': error_result.error_type,
                'severity': error_result.severity.value,
                'retry_recommended': error_result.retry_recommended,
                'fallback_used': error_result.fallback_used
            }
        )

