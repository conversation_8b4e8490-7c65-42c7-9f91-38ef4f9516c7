"""
Service Manager - Centralized service initialization and management.
"""

import asyncio
from typing import Optional, Dict, Any
from src.shared.error_handling.logging import get_logger
from src.shared.config.config_manager import config
from src.shared.cache.cache_service import cache_service
from src.shared.metrics.metrics_service import metrics_service
import pandas as pd

logger = get_logger(__name__)

class ServiceManager:
    """Manages all bot services and their initialization."""
    
    def __init__(self):
        self.services: Dict[str, Any] = {}
        self.initialized = False
        # Add configuration to services
        self.services['config'] = config
    
    async def initialize(self):
        """Initialize all services."""
        if self.initialized:
            return
        
        logger.info("🔄 Initializing services...")
        
        # Initialize services in order
        await self._init_cache_service()
        await self._init_metrics_service()
        await self._init_database()
        await self._init_ai_service()
        await self._init_data_provider_aggregator()
        await self._init_rate_limiter()
        await self._init_watchlist_manager()
        await self._init_permissions()
        
        self.initialized = True
        logger.info("✅ All services initialized")
    
    async def _init_cache_service(self):
        """Initialize cache service."""
        try:
            await cache_service.initialize()
            self.services['cache_service'] = cache_service
            logger.info("✅ Cache service initialized")
        except Exception as e:
            logger.error(f"❌ Cache service initialization failed: {e}")
            self.services['cache_service'] = None
    
    async def _init_metrics_service(self):
        """Initialize metrics service."""
        try:
            self.services['metrics_service'] = metrics_service
            logger.info("✅ Metrics service initialized")
        except Exception as e:
            logger.error(f"❌ Metrics service initialization failed: {e}")
            self.services['metrics_service'] = None
    
    async def _init_database(self):
        """Initialize database connection."""
        try:
            from src.bot.database_manager import bot_db_manager
            success = await bot_db_manager.initialize()
            self.services['database'] = bot_db_manager if success else None
            logger.info(f"✅ Database: {'Connected' if success else 'Failed'}")
        except Exception as e:
            logger.error(f"❌ Database initialization failed: {e}")
            self.services['database'] = None
    
    async def _init_ai_service(self):
        """Initialize AI service."""
        try:
            logger.info("DEBUG: Starting AI service init - checking pandas availability")
            try:
                import pandas as pd
                logger.info(f"DEBUG: Pandas imported successfully - DataFrame: {hasattr(pd, 'DataFrame')}")
            except ImportError as pd_err:
                logger.error(f"DEBUG: Pandas import failed: {pd_err}")
                raise

            from src.shared.ai_services.unified_ai_processor import create_unified_processor as create_processor
            logger.info("DEBUG: Imported unified_ai_processor successfully")
            self.services['ai_service'] = create_processor(context=self)
            logger.info("✅ AI service initialized")
        except Exception as e:
            logger.error(f"❌ AI service initialization failed: {e}", exc_info=True)
            self.services['ai_service'] = None

    async def _init_data_provider_aggregator(self):
        """Initialize the data provider aggregator."""
        try:
            from src.shared.data_providers.aggregator import data_provider_aggregator
            self.services['data_provider_aggregator'] = data_provider_aggregator
            logger.info("✅ Data provider aggregator initialized")
        except Exception as e:
            logger.error(f"❌ Data provider aggregator initialization failed: {e}")
            self.services['data_provider_aggregator'] = None
    
    async def _init_rate_limiter(self):
        """Initialize rate limiter."""
        try:
            from src.bot.rate_limiter import rate_limiter
            self.services['rate_limiter'] = rate_limiter
            logger.info("✅ Rate limiter initialized")
        except Exception as e:
            logger.error(f"❌ Rate limiter initialization failed: {e}")
            self.services['rate_limiter'] = None
    
    async def _init_watchlist_manager(self):
        """Initialize watchlist manager."""
        try:
            # Initialize watchlist manager with unified database manager
            from src.shared.watchlist.supabase_manager import SupabaseWatchlistManager
            try:
                self.services['watchlist_manager'] = SupabaseWatchlistManager()
                logger.info("✅ Watchlist manager initialized")
            except Exception as e:
                self.services['watchlist_manager'] = None
                logger.warning(f"⚠️ Watchlist manager initialization failed: {e}")
        except Exception as e:
            logger.error(f"❌ Watchlist manager initialization failed: {e}")
            self.services['watchlist_manager'] = None
    
    async def _init_permissions(self):
        """Initialize permission checker."""
        try:
            from src.bot.permissions import DiscordPermissionChecker
            self.services['permission_checker'] = DiscordPermissionChecker()
            logger.info("✅ Permission checker initialized")
        except Exception as e:
            logger.error(f"❌ Permission checker initialization failed: {e}")
            self.services['permission_checker'] = None
    
    def get_service(self, name: str) -> Optional[Any]:
        """Get a service by name."""
        return self.services.get(name)
    
    async def cleanup(self):
        """Cleanup all services."""
        logger.info("🔄 Cleaning up services...")
        # Add cleanup logic for services that need it
        self.services.clear()
        self.initialized = False
        logger.info("✅ Services cleaned up")
