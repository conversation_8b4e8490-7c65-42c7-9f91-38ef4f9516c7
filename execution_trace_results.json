{"trace_results": [{"entry_file": "start_bot.py", "imported_files": ["start_bot.py"], "import_chain": ["start_bot.py"], "execution_path": ["start_bot.py::call::Path", "start_bot.py::call::str", "start_bot.py::call::create_bot", "start_bot.py::call::start_bot"], "import_graph": {}, "file_dependencies": {}}, {"entry_file": "start_enhanced_bot.py", "imported_files": ["start_enhanced_bot.py"], "import_chain": ["start_enhanced_bot.py"], "execution_path": ["start_enhanced_bot.py::function::signal_handler", "start_enhanced_bot.py::function::check_environment", "start_enhanced_bot.py::call::str", "start_enhanced_bot.py::call::graceful_shutdown", "start_enhanced_bot.py::call::create_bot", "start_enhanced_bot.py::call::check_environment", "start_enhanced_bot.py::call::start_bot", "start_enhanced_bot.py::call::Path", "start_enhanced_bot.py::call::main", "start_enhanced_bot.py::call::graceful_shutdown"], "import_graph": {}, "file_dependencies": {}}, {"entry_file": "start_ai_automation.py", "imported_files": ["start_ai_automation.py"], "import_chain": ["start_ai_automation.py"], "execution_path": ["start_ai_automation.py::class::AIAutomationSystem", "start_ai_automation.py::function::__init__", "start_ai_automation.py::function::signal_handler", "start_ai_automation.py::call::AIAutomationSystem", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::initialize_ai_report_scheduler", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::main", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::DiscordWebhookHandler", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::DiscordWebhookHandler", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::print", "start_ai_automation.py::call::len", "start_ai_automation.py::call::type", "start_ai_automation.py::call::type"], "import_graph": {}, "file_dependencies": {}}], "usage_analysis": {"total_imported_files": 3, "total_files_in_codebase": 481, "unused_files": ["bot/pipeline/commands/analyze/stages/__init__.py", "api/routes/bot_health.py", "bot/pipeline/commands/ask/architecture/__init__.py", "bot/pipeline/commands/analyze/pipeline.py", "bot/pipeline/commands/ask/core/stage_executor.py", "analysis/orchestration/enhancement_strategy.py", "analysis/probability/probability_engine.py", "bot/pipeline/monitoring/__init__.py", "bot/advanced_trading_strategies.py", "api/routers/__init__.py", "analysis/risk/calculators/volatility_calculator.py", "bot/pipeline/commands/ask/deployment/monitoring.py", "data/models/__init__.py", "core/prompts/commands/ask_prompt_replacement_ideas.py", "shared/ai_services/local_fallback_ai.py", "analysis/utils/data_validators.py", "bot/pipeline/commands/ask/performance/__init__.py", "bot/extensions/status.py", "shared/data_providers/hybrid_mcp_provider.py", "bot/pipeline/commands/ask/core/stage_manager.py", "shared/ai_services/enhanced_ai_client.py", "bot/pipeline/commands/ask/compliance/audit_logger.py", "bot/pipeline/commands/ask/deployment/documentation.py", "core/risk_management/atr_calculator.py", "bot/pipeline/commands/ask/stages/analysis_components.py", "bot/pipeline/commands/ask/security/auth_manager.py", "shared/mcp/docker_mcp_client.py", "bot/client.py", "shared/technical_analysis/volume_analyzer.py", "api/routes/market_data.py", "bot/pipeline/core/pipeline_optimizer.py", "analysis/templates/analysis_response_template.py", "shared/ai_services/enhanced_symbol_extractor.py", "bot/utils/disclaimer_manager.py", "bot/watchlist_alerts.py", "core/prompts/commands/ask_prompts.py", "bot/pipeline/commands/ask/security/test_rate_limiter.py", "bot/pipeline/commands/ask/stages/ai_synthesizer.py", "data/cache/__init__.py", "core/pipeline_engine.py", "bot/pipeline/commands/ask/quality/type_safety.py", "bot/pipeline/commands/ask/cleanup/__init__.py", "api/data/providers/modules/validation.py", "bot/database_manager.py", "bot/extensions/zones.py", "api/schemas/feedback_schema.py", "bot/pipeline/commands/ask/performance/database_optimizer.py", "bot/extensions/batch_analyze.py", "bot/extensions/__init__.py", "bot/pipeline/commands/ask/cost/__init__.py", "bot/pipeline/commands/ask/security/security_scanner.py", "bot/pipeline/commands/__init__.py", "shared/watchlist/supabase_manager.py", "analysis/risk/calculators/beta_calculator.py", "core/formatting/text_formatting.py", "shared/technical_analysis/enhanced_calculator.py", "bot/pipeline/commands/ask/quality/documentation.py", "bot/pipeline/test_pipeline.py", "shared/ai_services/query_cache.py", "shared/config_loader.py", "core/prompts/base/personas.py", "bot/enhancements/__init__.py", "bot/pipeline/data/__init__.py", "bot/security/advanced_security.py", "bot/pipeline/commands/ask/audit/audit_logger.py", "bot/pipeline/core/context_manager.py", "bot/market_sentiment_analyzer.py", "shared/data_providers/alpha_vantage_mcp.py", "shared/data_providers/__init__.py", "bot/extensions/utility.py", "data/models/indicators.py", "api/data/providers/modules/config.py", "database/unified_db.py", "api/routes/dashboard.py", "api/data/cache_warming_scheduler.py", "analysis/__init__.py", "shared/data_validation.py", "data/__init__.py", "bot/pipeline/commands/ask/observability/metrics.py", "bot/pipeline/commands/ask/deployment/cicd_pipeline.py", "core/trade_scanner.py", "bot/audit/rate_limiter.py", "shared/ai_services/unified_ai_processor.py", "core/secure_cache.py", "database/migrations/__init__.py", "bot/extensions/ml_admin.py", "api/data/__init__.py", "shared/market_analysis/confidence_scorer.py", "shared/technical_analysis/__init__.py", "shared/utils/__init__.py", "bot/pipeline/commands/ask/pipeline.py", "bot/real_time_data_stream.py", "bot/pipeline/commands/ask/architecture/service_registry.py", "bot/__main__.py", "bot/pipeline/commands/ask/api/contracts.py", "bot/update_imports.py", "shared/ai/model_fine_tuner.py", "shared/database/usage_example.py", "shared/ai_chat/ai_client.py", "bot/pipeline/commands/ask/stages/query_analyzer.py", "analysis/fundamental/calculators/pe_calculator.py", "bot/monitoring/health_monitor.py", "bot/pipeline/shared/data_collectors/__init__.py", "shared/error_handling/logging.py", "shared/monitoring/pipeline_grader.py", "bot/pipeline/commands/analyze/stages/technical_analysis.py", "bot/pipeline/commands/ask/security/test_auth_manager.py", "api/data/providers/alpha_vantage.py", "api/webhooks/__init__.py", "bot/pipeline/ask/stages/conversation_memory_service.py", "analysis/risk/enhanced_risk_assessment.py", "shared/background/celery_app.py", "analysis/orchestration/analysis_orchestrator.py", "bot/pipeline/utils/__init__.py", "shared/redis/__init__.py", "shared/services/performance_monitor.py", "shared/data_providers/fallback_provider.py", "bot/pipeline/commands/analyze/stages/fetch_data.py", "bot/pipeline/utils/circuit_breaker.py", "bot/pipeline/commands/ask/api/backward_compatibility.py", "bot/extensions/alerts.py", "bot/pipeline/commands/ask/security/rate_limiter.py", "bot/audit/request_visualizer.py", "bot/pipeline/commands/ask/deployment/__init__.py", "shared/services/optimization_service.py", "bot/pipeline/commands/ask/errors/__init__.py", "shared/data_pipeline/websocket_handler.py", "bot/pipeline/commands/ask/modernization/modern_python.py", "bot/pipeline/commands/ask/tools/__init__.py", "core/automation/discord_handler.py", "bot/pipeline/commands/ask/config/config_manager.py", "core/risk_management/__init__.py", "shared/data_providers/health_monitor.py", "bot/pipeline/commands/ask/compliance/__init__.py", "bot/pipeline/commands/ask/security/simplified_security.py", "analysis/risk/calculators/__init__.py", "bot/pipeline/commands/ask/observability/__init__.py", "core/validation/__init__.py", "bot/pipeline/commands/ask/errors/fallback_strategy.py", "shared/error_handling/fallback.py", "analysis/ai/ml_training_service.py", "shared/data_providers/base.py", "bot/pipeline/ask/__init__.py", "core/watchlist/__init__.py", "bot/pipeline/commands/ask/performance/benchmark.py", "api/routes/health.py", "shared/technical_analysis/enhanced_indicators.py", "shared/ai_debugger/local_pattern_debugger.py", "bot/client_audit_integration.py", "shared/ai_services/enhanced_intent_detector.py", "shared/sentiment/sentiment_analyzer.py", "core/prompts/services/intent_detection.py", "mcp_server/free_mcp_registry.py", "bot/pipeline/commands/ask/cache/unified_cache.py", "core/prompts/utils/formatters.py", "shared/monitoring/__init__.py", "shared/data_providers/alpaca_provider.py", "shared/data_providers/unified_base.py", "bot/pipeline/commands/ask/observability/log_analyzer.py", "bot/pipeline/commands/ask/errors/error_manager.py", "services/__init__.py", "api/config.py", "core/formatting/response_templates.py", "shared/technical_analysis/zones.py", "shared/market_analysis/signal_analyzer.py", "api/__init__.py", "shared/technical_analysis/indicators.py", "__init__.py", "core/prompts/base/compliance.py", "shared/market_analysis/utils.py", "shared/watchlist/bot_manager.py", "analysis/ai/enhancement_strategy.py", "core/prompts/commands/__init__.py", "shared/market_analysis/__init__.py", "bot/extensions/portfolio.py", "shared/technical_analysis/strategy_calculator.py", "core/automation/report_scheduler.py", "shared/ai_chat/data_fetcher.py", "bot/core/error_handler.py", "core/prompts/services/security_analysis.py", "shared/watchlist/webhook_manager.py", "bot/pipeline/commands/ask/modernization/containerization.py", "shared/data_pipeline/buffer_manager.py", "shared/background/tasks/market_intelligence.py", "bot/extensions/performance_monitor.py", "shared/__init__.py", "shared/ai_services/timeout_manager.py", "bot/pipeline/core/__init__.py", "bot/pipeline/commands/ask/cleanup/legacy_archiver.py", "core/prompts/base/system_prompts.py", "shared/mcp/mcp_client_config.py", "bot/pipeline/utils/metrics.py", "shared/analytics/performance_tracker.py", "shared/metrics/metrics_service.py", "database/migrations/env.py", "shared/ai_services/ai_chat_processor.py", "shared/redis/redis_manager.py", "api/routers/market_data.py", "core/automation/analysis_scheduler.py", "mcp_server/internal_tools_mcp_server.py", "shared/ai_services/query_router.py", "analysis/fundamental/calculators/__init__.py", "bot/security/__init__.py", "api/main.py", "core/formatting/analysis_template.py", "bot/utils/input_sanitizer.py", "core/formatting/technical_analysis.py", "bot/pipeline/commands/ask/api/integration.py", "data/cache/manager.py", "bot/extensions/watchlist.py", "shared/ai_services/openrouter_key.py", "core/prompts/models.py", "shared/ai_services/fast_price_lookup.py", "bot/pipeline/commands/ask/core/__init__.py", "bot/pipeline/commands/ask/observability/tracer.py", "bot/pipeline/core/circuit_breaker.py", "analysis/orchestration/__init__.py", "shared/ai/recommendation_engine.py", "api/middleware/__init__.py", "bot/__init__.py", "bot/rate_limiter.py", "bot/pipeline/commands/ask/observability/logger.py", "bot/utils/enhanced_input_validator.py", "api/data/metrics.py", "bot/enhancements/pipeline_visualizer.py", "shared/utils/symbol_extraction.py", "shared/monitoring/performance_monitor.py", "api/routes/analytics.py", "bot/pipeline/shared/__init__.py", "bot/pipeline/commands/analyze/__init__.py", "bot/pipeline/commands/ask/architecture/event_bus.py", "bot/pipeline/commands/ask/tools/fallback_handler.py", "api/data/providers/modules/__init__.py", "bot/pipeline/shared/formatters/__init__.py", "shared/ai_services/ai_tool_registry.py", "analysis/probability/probability_response_service.py", "bot/pipeline/commands/ask/stages/simplified_tool_orchestrator.py", "bot/events/__init__.py", "shared/ai_chat/models.py", "shared/technical_analysis/options_greeks_calculator.py", "shared/technical_analysis/multi_timeframe_analyzer.py", "bot/pipeline_framework.py", "bot/extensions/analyze.py", "analysis/ai/recommendation_engine.py", "bot/monitoring/__init__.py", "core/automation/report_engine.py", "main.py", "core/monitoring_pkg/bot_monitor.py", "analysis/ai/ml_models.py", "analysis/technical/__init__.py", "core/logger.py", "core/response_generator.py", "shared/technical_analysis/signal_generator.py", "security/middleware.py", "database/models/analysis.py", "api/middleware/security.py", "shared/database/__init__.py", "core/risk_management/compliance_framework.py", "core/enums/stock_analysis.py", "bot/enhancements/discord_ux.py", "api/routes/__init__.py", "shared/metrics/unified_metrics_service.py", "bot/token_validator.py", "bot/alerts/real_time_alerts.py", "analysis/utils/__init__.py", "bot/pipeline/commands/ask/executor.py", "shared/configuration/__init__.py", "bot/audit/request_visualizer_patch.py", "api/schemas/metrics_schema.py", "shared/data_providers/polygon_provider.py", "bot/pipeline/commands/ask/performance/connection_pool.py", "bot/pipeline/commands/ask/performance/smart_cache.py", "bot/pipeline/commands/ask/cache/intelligent_cache.py", "bot/pipeline/commands/watchlist/__init__.py", "shared/watchlist/__init__.py", "shared/background/tasks/__init__.py", "bot/pipeline/commands/ask/modernization/dependency_manager.py", "shared/ai_services/__init__.py", "bot/pipeline/commands/ask/stages/intent_detector.py", "bot/utils/rate_limiter.py", "bot/setup_audit.py", "shared/ai_services/circuit_breaker.py", "bot/pipeline/commands/ask/stages/response_generator.py", "bot/ai/unified_intent_system.py", "bot/pipeline/commands/ask/core/controller.py", "core/prompts/utils/validation.py", "database/models/interactions.py", "bot/pipeline/commands/ask/api/versioning.py", "shared/background/__init__.py", "bot/pipeline/commands/ask/core/error_coordinator.py", "shared/metrics/naming_conventions.py", "shared/monitoring/pipeline_monitor.py", "bot/pipeline/commands/ask/ask_config.py", "database/models/market_data.py", "bot/pipeline/commands/ask/security/test_security_scanner.py", "bot/pipeline/commands/analyze/stages/price_targets.py", "shared/monitoring/step_logger.py", "bot/pipeline/commands/ask/compliance/compliance_logger.py", "shared/ai_services/smart_model_router.py", "bot/pipeline/commands/analyze/stages/report_generator.py", "bot/pipeline/commands/ask/__init__.py", "core/feedback_mechanism.py", "bot/main.py", "bot/core/services.py", "mcp_server/trading_mcp_server.py", "bot/extensions/error_handler.py", "shared/ai_debugger/live_ai_debugger.py", "services/analytics_service.py", "bot/pipeline/commands/ask/config/environment_profiles.py", "shared/ai_services/cross_validation_ai.py", "shared/market_analysis/unified_signal_analyzer.py", "bot/pipeline/commands/ask/performance/async_optimizer.py", "core/prompts/__init__.py", "api/middleware/security_utils.py", "shared/utils/lazy_import.py", "shared/ai_chat/response_formatter.py", "bot/audit/__init__.py", "database/models/__init__.py", "core/prompts/utils/context_injection.py", "shared/utils/deprecation.py", "bot/pipeline/commands/ask/cost/resource_optimizer.py", "shared/cache/cache_service.py", "core/exceptions.py", "bot/performance_analytics.py", "shared/ai_services/tool_registry.py", "bot/pipeline/commands/ask/security/input_validator.py", "api/data/providers/data_source_manager.py", "analysis/sentiment/enhanced_sentiment_analyzer.py", "shared/background/tasks/indicators.py", "bot/pipeline/shared/validators/__init__.py", "api/data/constants.py", "templates/__init__.py", "bot/pipeline/commands/ask/config/feature_flags.py", "templates/analysis_response.py", "bot/pipeline/commands/ask/api/__init__.py", "core/utils.py", "core/prompts/commands/general_prompts.py", "analysis/ai/calculators/sentiment_calculator.py", "shared/ai/__init__.py", "shared/ai_debugger/__init__.py", "shared/ai_services/fact_verifier.py", "bot/pipeline/commands/ask/observability/health_checker.py", "shared/technical_analysis/test_indicators.py", "shared/technical_analysis/config.py", "analysis/risk/__init__.py", "shared/data_providers/aggregator.py", "mcp_server/__init__.py", "bot/pipeline/commands/ask/modernization/__init__.py", "analysis/templates/__init__.py", "analysis/fundamental/calculators/growth_calculator.py", "analysis/enhanced_evaluator.py", "shared/ai_services/simple_model_config.py", "bot/pipeline/commands/ask/security/test_input_validator.py", "bot/pipeline/commands/ask/cleanup/dead_code_analyzer.py", "core/prompts/unified_prompts.py", "bot/pipeline/commands/ask/stages/data_collector.py", "shared/ai_services/ai_security_detector.py", "shared/ai_services/anti_hallucination_prompt.py", "shared/data_providers/yfinance_provider.py", "shared/configuration/validators.py", "core/market_calendar.py", "shared/ai_chat/processor.py", "analysis/probability/monte_carlo_simulator.py", "core/data_quality_validator.py", "core/automation/__init__.py", "bot/pipeline/ask/stages/__init__.py", "bot/pipeline/__init__.py", "shared/ai_services/ai_processor_robust.py", "data/models/stock_data.py", "core/prompts/commands/analyze_prompts.py", "core/automation/report_formatter.py", "shared/ai/depth_controller.py", "shared/ai_chat/config.py", "shared/ai_services/response_synthesizer.py", "shared/ai_services/rate_limit_handler.py", "bot/pipeline/commands/analyze/multi_timeframe_pipeline.py", "bot/extensions/ask.py", "bot/metrics_collector.py", "shared/data_providers/enhanced_error_handler.py", "bot/pipeline/commands/ask/performance/request_batcher.py", "shared/monitoring/intelligent_grader.py", "bot/pipeline/commands/ask/cache/__init__.py", "database/__init__.py", "api/data/providers/finnhub.py", "shared/validation/enhanced_fact_checker.py", "bot/pipeline/commands/ask/cost/cost_tracker.py", "shared/watchlist/models.py", "shared/market_analysis/signals.py", "core/config_manager.py", "analysis/fundamental/__init__.py", "bot/utils/component_checker.py", "logs/__init__.py", "api/routes/feedback.py", "bot/pipeline/commands/ask/cleanup/test_consolidator.py", "api/data/market_data_service.py", "bot/permissions.py", "shared/technical_analysis/calculator.py", "bot/pipeline/core/pipeline_engine.py", "bot/pipeline/commands/ask/compliance/data_manager.py", "core/prompts/services/__init__.py", "core/validation/financial_validator.py", "core/monitoring_pkg/performance_tracker.py", "core/formatting/__init__.py", "shared/data_providers/finnhub_provider.py", "shared/sentiment/__init__.py", "database/models/alerts.py", "bot/pipeline/commands/analyze/stages/report_template.py", "shared/ai_services/simple_query_analyzer.py", "shared/watchlist/base_manager.py", "bot/pipeline/commands/ask/config/__init__.py", "bot/pipeline/commands/ask/stages/__init__.py", "shared/analytics/__init__.py", "api/data/providers/base.py", "core/scheduler.py", "analysis/risk/assessment.py", "shared/ai_services/ai_service_wrapper.py", "database/unified_client.py", "analysis/probability/__init__.py", "core/prompts/services/text_parsing.py", "core/monitoring_pkg/__init__.py", "database/repositories/__init__.py", "shared/ai_services/intelligent_text_parser.py", "bot/pipeline/commands/ask/config/secrets_manager.py", "bot/utils/error_handler.py", "bot/pipeline/commands/ask/quality/__init__.py", "bot/ai/context_aware_processor.py", "bot/watchlist_realtime.py", "bot/pipeline/commands/ask/security/__init__.py", "api/data/providers/__init__.py", "database/config.py", "database/query_wrapper.py", "analysis/ai/calculators/__init__.py", "api/routes/debug.py", "core/prompts/services/anti_hallucination.py", "bot/pipeline/logs/__init__.py", "mcp_server/internal_tools_client.py", "bot/pipeline/commands/ask/stages/formatter.py", "bot/pipeline/commands/analyze/stages/enhanced_analysis.py", "shared/error_handling/retry.py", "analysis/ai/__init__.py", "bot/pipeline/commands/ask/quality/code_standards.py", "database/query_optimizer.py", "bot/extensions/recommendations.py", "analysis/fundamental/metrics.py", "api/routes/metrics.py", "api/data/providers/modules/rate_limiting.py", "shared/error_handling/__init__.py", "bot/pipeline/commands/ask/audit/__init__.py", "api/data/cache.py", "api/data/providers/polygon.py", "shared/config/config_manager.py", "mcp_server/mcp_client_integration.py", "bot/pipeline/core/parallel_pipeline.py", "bot/extensions/help.py", "bot/pipeline/commands/ask/performance/resource_manager.py", "api/data/providers/modules/auditing.py", "bot/client_with_monitoring.py", "shared/services/enhanced_performance_optimizer.py", "shared/utils/discord_helpers.py", "bot/risk_management_system.py", "api/data/scheduled_tasks.py", "core/prompts/base/__init__.py", "shared/ai_chat/__init__.py", "shared/technical_analysis/unified_calculator.py", "bot/pipeline/commands/analyze/parallel_pipeline.py", "bot/pipeline/performance_optimizer.py", "bot/pipeline/commands/ask/tools/mcp_manager.py", "shared/data_providers/alpha_vantage.py", "bot/utils/__init__.py", "shared/ai_chat/fallbacks.py", "bot/core/bot.py", "api/analytics/__init__.py", "bot/audit/session_manager.py", "security/__init__.py", "core/prompts/utils/__init__.py", "core/prompts/templates/__init__.py", "core/prompts/prompt_manager.py", "bot/pipeline/commands/watchlist/stages/__init__.py", "core/enums/__init__.py", "api/schemas/__init__.py", "core/__init__.py"], "unused_count": 481, "commonly_used_files": {"start_bot.py": 1, "start_enhanced_bot.py": 1, "start_ai_automation.py": 1}, "entry_point_usage": {"start_bot.py": {"imported_count": 1, "imported_files": ["start_bot.py"]}, "start_enhanced_bot.py": {"imported_count": 1, "imported_files": ["start_enhanced_bot.py"]}, "start_ai_automation.py": {"imported_count": 1, "imported_files": ["start_ai_automation.py"]}}, "usage_percentage": 0.6237006237006237}}