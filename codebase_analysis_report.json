{
  "summary": {
    "total_files": 479,
    "total_lines": 126579,
    "total_size_mb": 4.68,
    "modules_count": 123,
    "duplicates_count": 1128,
    "circular_deps_count": 0,
    "architecture_issues_count": 56
  },
  "modules": {
    "root": {
      "path": "root",
      "file_count": 2,
      "total_lines": 255,
      "total_size": 9439,
      "files": [
        {
          "path": "main.py",
          "size": 9439,
          "lines": 255,
          "imports": [
            "asyncio",
            "json",
            "structlog",
            "datetime.datetime",
            "typing.Dict",
            "typing.Any",
            "typing.List",
            "fastapi.FastAPI",
            "fastapi.Request",
            "fastapi.HTTPException",
            "fastapi.BackgroundTasks",
            "fastapi.responses.JSONResponse",
            "fastapi.middleware.cors.CORSMiddleware",
            "security.SecurityMiddleware",
            "security.create_security_middleware",
            "src.shared.ai_services.intelligent_text_parser.PineScriptAlertParser",
            "src.shared.ai_services.intelligent_text_parser.ParsedAlert",
            "src.database.unified_db.UnifiedDatabaseManager",
            "os",
            "uvicorn",
            "os",
            "src.bot.monitoring.health_monitor.BotHealthMonitor",
            "src.bot.core.services.restart_bot_service",
            "src.shared.monitoring.performance_monitor.performance_monitor"
          ],
          "classes": [],
          "functions": [],
          "complexity": 19,
          "dependencies": 