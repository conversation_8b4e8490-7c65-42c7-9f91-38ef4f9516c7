#!/usr/bin/env python3
"""
Import Chain Analyzer
Analyzes import statements in files to understand the dependency chain
without actually executing the code.
"""

import os
import ast
import json
from pathlib import Path
from typing import Set, List, Dict, Any, Tuple
from collections import defaultdict, deque

class ImportChainAnalyzer:
    """Analyzes import chains in the codebase"""
    
    def __init__(self, src_path: str):
        self.src_path = Path(src_path)
        self.files: Dict[str, Dict[str, Any]] = {}
        self.import_graph: Dict[str, Set[str]] = defaultdict(set)
        self.reverse_graph: Dict[str, Set[str]] = defaultdict(set)
        self.entry_points = [
            "start_bot.py",
            "start_enhanced_bot.py", 
            "start_ai_automation.py"
        ]
        
    def analyze(self) -> Dict[str, Any]:
        """Analyze the entire codebase"""
        print("🔍 Analyzing import chains...")
        
        # Step 1: Scan all files and extract imports
        self._scan_all_files()
        
        # Step 2: Build import graph
        self._build_import_graph()
        
        # Step 3: Find reachable files from entry points
        reachable_files = self._find_reachable_files()
        
        # Step 4: Analyze unused files
        unused_files = self._find_unused_files(reachable_files)
        
        # Step 5: Find critical files (heavily imported)
        critical_files = self._find_critical_files()
        
        return {
            "total_files": len(self.files),
            "reachable_files": reachable_files,
            "unused_files": unused_files,
            "critical_files": critical_files,
            "import_graph": {k: list(v) for k, v in self.import_graph.items()},
            "file_details": self.files
        }
    
    def _scan_all_files(self):
        """Scan all Python files and extract imports"""
        print("📁 Scanning files...")
        
        for py_file in self.src_path.rglob("*.py"):
            if "__pycache__" in str(py_file):
                continue
            
            rel_path = str(py_file.relative_to(self.src_path))
            
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                imports = self._extract_imports(tree)
                
                self.files[rel_path] = {
                    "path": rel_path,
                    "size": py_file.stat().st_size,
                    "lines": len(content.splitlines()),
                    "imports": imports,
                    "is_entry_point": rel_path in self.entry_points
                }
                
            except Exception as e:
                print(f"⚠️  Error parsing {rel_path}: {e}")
                self.files[rel_path] = {
                    "path": rel_path,
                    "size": py_file.stat().st_size,
                    "lines": 0,
                    "imports": [],
                    "is_entry_point": rel_path in self.entry_points,
                    "error": str(e)
                }
    
    def _extract_imports(self, tree) -> List[str]:
        """Extract all imports from AST"""
        imports = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ""
                for alias in node.names:
                    if module:
                        imports.append(f"{module}.{alias.name}")
                    else:
                        imports.append(alias.name)
        return imports
    
    def _build_import_graph(self):
        """Build the import dependency graph"""
        print("🔗 Building import graph...")
        
        for file_path, file_info in self.files.items():
            for import_name in file_info["imports"]:
                resolved_path = self._resolve_import(import_name, file_path)
                if resolved_path:
                    self.import_graph[file_path].add(resolved_path)
                    self.reverse_graph[resolved_path].add(file_path)
    
    def _resolve_import(self, import_name: str, from_file: str) -> str:
        """Resolve import to actual file path"""
        # Handle relative imports
        if import_name.startswith('.'):
            from_dir = Path(from_file).parent
            parts = import_name.split('.')
            for part in parts[1:]:
                if part:
                    from_dir = from_dir / part
            
            # Try different extensions
            for ext in ['.py', '']:
                candidate = from_dir.with_suffix(ext)
                if candidate.exists():
                    return str(candidate.relative_to(self.src_path))
            
            # Try __init__.py
            candidate = from_dir / "__init__.py"
            if candidate.exists():
                return str(candidate.relative_to(self.src_path))
        
        # Handle absolute imports starting with src
        if import_name.startswith('src.'):
            module_path = import_name[4:]  # Remove 'src.' prefix
            candidate = self.src_path / f"{module_path}.py"
            if candidate.exists():
                return str(candidate.relative_to(self.src_path))
            # Try __init__.py
            candidate = self.src_path / module_path / "__init__.py"
            if candidate.exists():
                return str(candidate.relative_to(self.src_path))
        
        # Try to find the file
        for py_file in self.src_path.rglob("*.py"):
            if "__pycache__" in str(py_file):
                continue
            rel_path = str(py_file.relative_to(self.src_path))
            if rel_path.replace('/', '.').replace('.py', '') == import_name:
                return rel_path
        
        return None
    
    def _find_reachable_files(self) -> Set[str]:
        """Find all files reachable from entry points"""
        print("🎯 Finding reachable files...")
        
        reachable = set()
        queue = deque()
        
        # Start with entry points
        for entry_point in self.entry_points:
            if entry_point in self.files:
                queue.append(entry_point)
                reachable.add(entry_point)
        
        # BFS to find all reachable files
        while queue:
            current_file = queue.popleft()
            for imported_file in self.import_graph.get(current_file, []):
                if imported_file not in reachable:
                    reachable.add(imported_file)
                    queue.append(imported_file)
        
        return reachable
    
    def _find_unused_files(self, reachable_files: Set[str]) -> List[str]:
        """Find files that are not reachable from any entry point"""
        all_files = set(self.files.keys())
        unused = all_files - reachable_files
        return sorted(list(unused))
    
    def _find_critical_files(self) -> List[Tuple[str, int]]:
        """Find files that are imported by many other files"""
        import_counts = defaultdict(int)
        for file_path, imported_files in self.import_graph.items():
            for imported_file in imported_files:
                import_counts[imported_file] += 1
        
        return sorted(import_counts.items(), key=lambda x: x[1], reverse=True)

def print_analysis(analysis: Dict[str, Any]):
    """Print analysis results"""
    print("\n" + "="*80)
    print("📊 IMPORT CHAIN ANALYSIS RESULTS")
    print("="*80)
    
    print(f"📁 Total files: {analysis['total_files']}")
    print(f"✅ Reachable files: {len(analysis['reachable_files'])}")
    print(f"❌ Unused files: {len(analysis['unused_files'])}")
    print(f"📈 Usage percentage: {(len(analysis['reachable_files']) / analysis['total_files']) * 100:.1f}%")
    
    print(f"\n🔥 MOST CRITICAL FILES (Top 10):")
    for file_path, count in analysis['critical_files'][:10]:
        print(f"  - {file_path}: imported by {count} files")
    
    print(f"\n🗑️  UNUSED FILES (Top 20):")
    for file_path in analysis['unused_files'][:20]:
        file_info = analysis['file_details'][file_path]
        print(f"  - {file_path} ({file_info['lines']} lines)")
    
    print(f"\n📊 REACHABLE FILES BY MODULE:")
    module_counts = defaultdict(int)
    for file_path in analysis['reachable_files']:
        module = file_path.split('/')[0] if '/' in file_path else 'root'
        module_counts[module] += 1
    
    for module, count in sorted(module_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"  - {module}: {count} files")

def generate_cleanup_plan(analysis: Dict[str, Any]) -> Dict[str, Any]:
    """Generate a cleanup plan based on analysis"""
    plan = {
        "safe_to_delete": [],
        "keep_but_consolidate": [],
        "critical_files": [],
        "entry_points": [],
        "recommendations": []
    }
    
    # Files safe to delete (unused)
    plan["safe_to_delete"] = analysis['unused_files']
    
    # Critical files to keep
    plan["critical_files"] = [f[0] for f in analysis['critical_files'][:20]]
    
    # Entry points
    plan["entry_points"] = [f for f in analysis['reachable_files'] if f in ['start_bot.py', 'start_enhanced_bot.py', 'start_ai_automation.py']]
    
    # Recommendations
    if len(analysis['unused_files']) > 100:
        plan["recommendations"].append("Delete unused files to reduce complexity")
    
    if len(analysis['critical_files']) > 50:
        plan["recommendations"].append("Consolidate critical files to reduce coupling")
    
    return plan

def main():
    """Main function"""
    print("🚀 Starting Import Chain Analysis")
    
    analyzer = ImportChainAnalyzer("src(old)")
    analysis = analyzer.analyze()
    
    print_analysis(analysis)
    
    # Generate cleanup plan
    cleanup_plan = generate_cleanup_plan(analysis)
    
    print(f"\n📋 CLEANUP PLAN:")
    print(f"🗑️  Safe to delete: {len(cleanup_plan['safe_to_delete'])} files")
    print(f"🔧 Keep but consolidate: {len(cleanup_plan['keep_but_consolidate'])} files")
    print(f"⭐ Critical files: {len(cleanup_plan['critical_files'])} files")
    print(f"🚀 Entry points: {len(cleanup_plan['entry_points'])} files")
    
    for rec in cleanup_plan['recommendations']:
        print(f"💡 {rec}")
    
    # Save results
    with open("import_chain_analysis.json", "w") as f:
        json.dump({
            "analysis": analysis,
            "cleanup_plan": cleanup_plan
        }, f, indent=2)
    
    print(f"\n✅ Analysis complete!")
    print(f"📄 Results saved to: import_chain_analysis.json")
    
    return analysis, cleanup_plan

if __name__ == "__main__":
    main()
