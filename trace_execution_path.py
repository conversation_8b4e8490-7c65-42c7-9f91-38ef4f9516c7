#!/usr/bin/env python3
"""
Execution Path Tracer
Traces the actual import chain and execution path from start_bot.py
through Discord bot startup to understand which files are actually used.
"""

import sys
import os
import ast
import importlib
import traceback
from pathlib import Path
from typing import Set, List, Dict, Any
from collections import defaultdict, deque
import json

class ExecutionTracer:
    """Traces execution path and imports"""
    
    def __init__(self, src_path: str):
        self.src_path = Path(src_path)
        self.imported_files: Set[str] = set()
        self.import_chain: List[str] = []
        self.execution_path: List[str] = []
        self.import_graph: Dict[str, Set[str]] = defaultdict(set)
        self.file_dependencies: Dict[str, Set[str]] = defaultdict(set)
        self.entry_points = [
            "start_bot.py",
            "start_enhanced_bot.py", 
            "start_ai_automation.py",
            "src/main.py",
            "src/bot/main.py",
            "src/bot/__main__.py"
        ]
        
    def trace_from_entry_point(self, entry_file: str) -> Dict[str, Any]:
        """Trace execution from a specific entry point"""
        print(f"🔍 Tracing execution from: {entry_file}")
        
        try:
            # Add src to Python path
            if str(self.src_path) not in sys.path:
                sys.path.insert(0, str(self.src_path))
            
            # Start tracing
            self.imported_files.clear()
            self.import_chain.clear()
            self.execution_path.clear()
            
            # Try to find the entry file
            entry_path = None
            for possible_path in [
                entry_file,
                self.src_path / entry_file,
                Path(entry_file)
            ]:
                if Path(possible_path).exists():
                    entry_path = Path(possible_path)
                    break
            
            if not entry_path:
                print(f"❌ Entry file not found: {entry_file}")
                return {}
            
            # Trace the execution
            self._trace_file(entry_path)
            
            return {
                "entry_file": str(entry_path),
                "imported_files": list(self.imported_files),
                "import_chain": self.import_chain.copy(),
                "execution_path": self.execution_path.copy(),
                "import_graph": {k: list(v) for k, v in self.import_graph.items()},
                "file_dependencies": {k: list(v) for k, v in self.file_dependencies.items()}
            }
            
        except Exception as e:
            print(f"❌ Error tracing {entry_file}: {e}")
            traceback.print_exc()
            return {}
    
    def _trace_file(self, file_path: Path, depth: int = 0):
        """Recursively trace a file and its imports"""
        if depth > 20:  # Prevent infinite recursion
            return
        
        # Convert to relative path
        try:
            rel_path = str(file_path.relative_to(self.src_path))
        except ValueError:
            rel_path = str(file_path)
        
        if rel_path in self.imported_files:
            return  # Already processed
        
        self.imported_files.add(rel_path)
        self.import_chain.append(rel_path)
        
        print(f"{'  ' * depth}📄 Processing: {rel_path}")
        
        try:
            # Read and parse the file
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            # Extract imports
            imports = self._extract_imports(tree)
            
            # Process each import
            for import_name in imports:
                resolved_path = self._resolve_import(import_name, file_path)
                if resolved_path:
                    self.import_graph[rel_path].add(resolved_path)
                    self.file_dependencies[rel_path].add(resolved_path)
                    self._trace_file(resolved_path, depth + 1)
            
            # Extract function calls and class instantiations
            self._extract_execution_path(tree, rel_path)
            
        except Exception as e:
            print(f"{'  ' * depth}⚠️  Error processing {rel_path}: {e}")
    
    def _extract_imports(self, tree) -> List[str]:
        """Extract all imports from AST"""
        imports = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ""
                for alias in node.names:
                    if module:
                        imports.append(f"{module}.{alias.name}")
                    else:
                        imports.append(alias.name)
        return imports
    
    def _resolve_import(self, import_name: str, from_file: Path) -> Path:
        """Try to resolve import to actual file path"""
        # Handle relative imports
        if import_name.startswith('.'):
            from_dir = from_file.parent
            parts = import_name.split('.')
            # Skip the first dot
            for part in parts[1:]:
                if part:
                    from_dir = from_dir / part
            # Try different extensions
            for ext in ['.py', '']:
                candidate = from_dir.with_suffix(ext)
                if candidate.exists():
                    return candidate
            # Try __init__.py
            candidate = from_dir / '__init__.py'
            if candidate.exists():
                return candidate
        
        # Handle absolute imports starting with src
        if import_name.startswith('src.'):
            module_path = import_name[4:]  # Remove 'src.' prefix
            candidate = self.src_path / f"{module_path}.py"
            if candidate.exists():
                return candidate
            # Try __init__.py
            candidate = self.src_path / module_path / "__init__.py"
            if candidate.exists():
                return candidate
        
        # Try to find the file in src
        for py_file in self.src_path.rglob("*.py"):
            if "__pycache__" in str(py_file):
                continue
            rel_path = str(py_file.relative_to(self.src_path))
            if rel_path.replace('/', '.').replace('.py', '') == import_name:
                return py_file
        
        return None
    
    def _extract_execution_path(self, tree, file_path: str):
        """Extract execution path from AST"""
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                self.execution_path.append(f"{file_path}::function::{node.name}")
            elif isinstance(node, ast.ClassDef):
                self.execution_path.append(f"{file_path}::class::{node.name}")
            elif isinstance(node, ast.Call):
                if hasattr(node.func, 'id'):
                    self.execution_path.append(f"{file_path}::call::{node.func.id}")

def analyze_usage_patterns(trace_results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze usage patterns from trace results"""
    all_imported = set()
    all_execution_paths = []
    entry_point_usage = {}
    
    for result in trace_results:
        if not result:
            continue
        
        entry_file = result["entry_file"]
        imported_files = set(result["imported_files"])
        execution_path = result["execution_path"]
        
        all_imported.update(imported_files)
        all_execution_paths.extend(execution_path)
        entry_point_usage[entry_file] = {
            "imported_count": len(imported_files),
            "imported_files": list(imported_files)
        }
    
    # Find commonly used files
    file_usage_count = defaultdict(int)
    for result in trace_results:
        if result:
            for file_path in result["imported_files"]:
                file_usage_count[file_path] += 1
    
    # Find unused files
    all_files = set()
    for py_file in Path("src(old)").rglob("*.py"):
        if "__pycache__" not in str(py_file):
            rel_path = str(py_file.relative_to(Path("src(old)")))
            all_files.add(rel_path)
    
    unused_files = all_files - all_imported
    
    return {
        "total_imported_files": len(all_imported),
        "total_files_in_codebase": len(all_files),
        "unused_files": list(unused_files),
        "unused_count": len(unused_files),
        "commonly_used_files": dict(sorted(file_usage_count.items(), key=lambda x: x[1], reverse=True)[:20]),
        "entry_point_usage": entry_point_usage,
        "usage_percentage": (len(all_imported) / len(all_files)) * 100 if all_files else 0
    }

def print_usage_analysis(analysis: Dict[str, Any]):
    """Print usage analysis results"""
    print("\n" + "="*80)
    print("📊 USAGE ANALYSIS RESULTS")
    print("="*80)
    
    print(f"📁 Total files in codebase: {analysis['total_files_in_codebase']}")
    print(f"✅ Files actually used: {analysis['total_imported_files']}")
    print(f"❌ Unused files: {analysis['unused_count']}")
    print(f"📈 Usage percentage: {analysis['usage_percentage']:.1f}%")
    
    print(f"\n🔥 MOST COMMONLY USED FILES:")
    for file_path, count in list(analysis['commonly_used_files'].items())[:10]:
        print(f"  - {file_path}: used {count} times")
    
    print(f"\n📊 ENTRY POINT USAGE:")
    for entry_file, usage in analysis['entry_point_usage'].items():
        print(f"  - {entry_file}: {usage['imported_count']} files imported")
    
    print(f"\n🗑️  UNUSED FILES (Top 20):")
    for file_path in sorted(analysis['unused_files'])[:20]:
        print(f"  - {file_path}")

def main():
    """Main function"""
    print("🚀 Starting Execution Path Tracing")
    
    # Initialize tracer
    tracer = ExecutionTracer("src(old)")
    
    # Trace from all possible entry points
    trace_results = []
    for entry_point in tracer.entry_points:
        print(f"\n{'='*60}")
        result = tracer.trace_from_entry_point(entry_point)
        if result:
            trace_results.append(result)
            print(f"✅ Successfully traced {entry_point}")
        else:
            print(f"❌ Failed to trace {entry_point}")
    
    # Analyze usage patterns
    print(f"\n{'='*60}")
    print("📊 Analyzing usage patterns...")
    analysis = analyze_usage_patterns(trace_results)
    print_usage_analysis(analysis)
    
    # Save detailed results
    with open("execution_trace_results.json", "w") as f:
        json.dump({
            "trace_results": trace_results,
            "usage_analysis": analysis
        }, f, indent=2)
    
    print(f"\n✅ Execution tracing complete!")
    print(f"📄 Detailed results saved to: execution_trace_results.json")
    
    return analysis

if __name__ == "__main__":
    main()
